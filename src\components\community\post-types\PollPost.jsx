import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input, Button } from '../../ui';
import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';

const PollPost = ({ onChange, value }) => {
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [duration, setDuration] = useState(7); // days

  const handleQuestionChange = (e) => {
    const newQuestion = e.target.value;
    setQuestion(newQuestion);
    updateValue(newQuestion, options, duration);
  };

  const handleOptionChange = (index, value) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
    updateValue(question, newOptions, duration);
  };

  const addOption = () => {
    if (options.length < 6) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
      updateValue(question, newOptions, duration);
    }
  };

  const handleDurationChange = (e) => {
    const newDuration = parseInt(e.target.value);
    setDuration(newDuration);
    updateValue(question, options, newDuration);
  };

  const updateValue = (newQuestion, newOptions, newDuration) => {
    onChange({
      question: newQuestion,
      options: newOptions.filter(opt => opt.trim() !== ''),
      duration: newDuration,
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Poll Question
        </label>
        <Input
          value={question}
          onChange={handleQuestionChange}
          placeholder="Ask your question"
          className="w-full"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Options
        </label>
        <div className="space-y-2">
          <AnimatePresence>
            {options.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center space-x-2"
              >
                <Input
                  value={option}
                  onChange={(e) => handleOptionChange(index, e.target.value)}
                  placeholder={`Option ${index + 1}`}
                  className="flex-1"
                />
                {options.length > 2 && (
                  <button
                    type="button"
                    onClick={() => removeOption(index)}
                    className="p-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {options.length < 6 && (
          <Button
            type="button"
            variant="secondary"
            onClick={addOption}
            className="mt-2"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Option
          </Button>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Poll Duration (days)
        </label>
        <select
          value={duration}
          onChange={handleDurationChange}
          className="block w-full rounded-md border-gray-300 dark:border-dark-700 bg-white dark:bg-dark-800 text-gray-900 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
        >
          <option value={1}>1 day</option>
          <option value={3}>3 days</option>
          <option value={7}>7 days</option>
          <option value={14}>14 days</option>
          <option value={30}>30 days</option>
        </select>
      </div>
    </div>
  );
};

export default PollPost; 
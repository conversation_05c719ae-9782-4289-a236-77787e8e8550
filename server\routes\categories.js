const express = require('express');
const router = express.Router();
const Community = require('../models/Community');
const auth = require('../middleware/auth');

// @route   GET api/categories
// @desc    Get all categories with community counts
// @access  Public
router.get('/', async (req, res) => {
  try {
    const categories = await Community.aggregate([
      {
        $group: {
          _id: '$category',
          communities: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: 0,
          id: '$_id',
          name: '$_id',
          communities: 1,
        },
      },
      {
        $sort: { communities: -1 },
      },
    ]);

    res.json(categories);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router; 
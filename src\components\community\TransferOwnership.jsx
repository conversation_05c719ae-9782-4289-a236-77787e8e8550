import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-hot-toast';
import { updateCommunity } from '../../store/slices/communitySlice';
import { Button, Input } from '../ui';
import { UserGroupIcon } from '@heroicons/react/24/outline';

const TransferOwnership = ({ community, onClose }) => {
  const dispatch = useDispatch();
  const [selectedMember, setSelectedMember] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const eligibleMembers = community.members.filter(
    (member) => member.userId !== community.adminId
  );

  const handleTransfer = async () => {
    if (!selectedMember) {
      toast.error('Please select a member to transfer ownership to');
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        updateCommunity({
          slug: community.slug,
          data: { adminId: selectedMember },
        })
      ).unwrap();
      toast.success('Ownership transferred successfully');
      onClose();
    } catch (error) {
      toast.error(error.message || 'Failed to transfer ownership');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Transfer Ownership
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-300">
          Select a member to transfer community ownership to. This action cannot be undone.
        </p>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select New Owner
          </label>
          <div className="space-y-2">
            {eligibleMembers.map((member) => (
              <button
                key={member.userId}
                className={`w-full flex items-center space-x-3 p-3 rounded-lg border ${
                  selectedMember === member.userId
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700'
                }`}
                onClick={() => setSelectedMember(member.userId)}
              >
                <img
                  src={member.avatar}
                  alt={member.username}
                  className="h-10 w-10 rounded-full"
                />
                <div className="flex-1 text-left">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {member.username}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {member.role}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleTransfer}
            loading={isSubmitting}
            disabled={!selectedMember}
          >
            <UserGroupIcon className="h-5 w-5 inline mr-2" />
            Transfer Ownership
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TransferOwnership; 
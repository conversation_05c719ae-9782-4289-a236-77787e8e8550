import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchFollowing } from '../store/slices/profileSlice';
import { Button } from '../components/ui/Button';

const FollowingPage = () => {
  const { userId } = useParams();
  const dispatch = useDispatch();
  const { following, isLoading } = useSelector((state) => state.profile);

  useEffect(() => {
    dispatch(fetchFollowing(userId));
  }, [dispatch, userId]);

  const handleFollowToggle = (followingId) => {
    // Implement follow/unfollow logic here
    console.log(`Toggle follow for ${followingId}`);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-3xl mx-auto p-4"
    >
      <h1 className="text-2xl font-bold text-white mb-6">Following</h1>
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse h-20" />
          ))}
        </div>
      ) : following && following.length > 0 ? (
        <div className="bg-background rounded-lg shadow-sm divide-y divide-gray-800">
          {following.map((followedUser) => (
            <div key={followedUser._id} className="flex items-center justify-between p-4">
              <Link to={`/profile/${followedUser.username}`} className="flex items-center space-x-3">
                <img src={followedUser.avatar} alt={followedUser.username} className="h-12 w-12 rounded-full" />
                <div>
                  <p className="font-semibold text-white">{followedUser.realName}</p>
                  <p className="text-sm text-gray-400">@{followedUser.username}</p>
                </div>
              </Link>
              <Button
                variant={followedUser.isFollowing ? 'secondary' : 'primary'}
                onClick={() => handleFollowToggle(followedUser._id)}
              >
                {followedUser.isFollowing ? 'Unfollow' : 'Follow'}
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-400 py-8">
          <p>This user isn't following anyone yet.</p>
        </div>
      )}
    </motion.div>
  );
};

export default FollowingPage; 
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Async thunks
export const fetchTrendingTopics = createAsyncThunk(
  'explore/fetchTrendingTopics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/explore/trending`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch trending topics');
    }
  }
);

export const fetchSuggestedPeople = createAsyncThunk(
  'explore/fetchSuggestedPeople',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/explore/suggested-people`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch suggested people');
    }
  }
);

const initialState = {
  trendingTopics: [],
  suggestedPeople: [],
  isLoading: false,
  error: null,
};

const exploreSlice = createSlice({
  name: 'explore',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch trending topics
      .addCase(fetchTrendingTopics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTrendingTopics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.trendingTopics = action.payload;
      })
      .addCase(fetchTrendingTopics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Fetch suggested people
      .addCase(fetchSuggestedPeople.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSuggestedPeople.fulfilled, (state, action) => {
        state.isLoading = false;
        state.suggestedPeople = action.payload;
      })
      .addCase(fetchSuggestedPeople.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = exploreSlice.actions;
export default exploreSlice.reducer; 
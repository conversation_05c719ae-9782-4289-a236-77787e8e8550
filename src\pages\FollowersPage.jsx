import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchFollowers } from '../store/slices/profileSlice';
import { Button } from '../components/ui/Button';

const FollowersPage = () => {
  const { userId } = useParams();
  const dispatch = useDispatch();
  const { followers, isLoading } = useSelector((state) => state.profile);

  useEffect(() => {
    dispatch(fetchFollowers(userId));
  }, [dispatch, userId]);

  const handleFollowToggle = (followerId) => {
    // Implement follow/unfollow logic here
    console.log(`Toggle follow for ${followerId}`);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-3xl mx-auto p-4"
    >
      <h1 className="text-2xl font-bold text-white mb-6">Followers</h1>
      {isLoading ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse h-20" />
          ))}
        </div>
      ) : followers && followers.length > 0 ? (
        <div className="bg-background rounded-lg shadow-sm divide-y divide-gray-800">
          {followers.map((follower) => (
            <div key={follower._id} className="flex items-center justify-between p-4">
              <Link to={`/profile/${follower.username}`} className="flex items-center space-x-3">
                <img src={follower.avatar} alt={follower.username} className="h-12 w-12 rounded-full" />
                <div>
                  <p className="font-semibold text-white">{follower.realName}</p>
                  <p className="text-sm text-gray-400">@{follower.username}</p>
                </div>
              </Link>
              <Button
                variant={follower.isFollowing ? 'secondary' : 'primary'}
                onClick={() => handleFollowToggle(follower._id)}
              >
                {follower.isFollowing ? 'Unfollow' : 'Follow'}
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center text-gray-400 py-8">
          <p>This user has no followers yet.</p>
        </div>
      )}
    </motion.div>
  );
};

export default FollowersPage; 
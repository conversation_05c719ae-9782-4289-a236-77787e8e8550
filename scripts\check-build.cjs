#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Checking production build...\n');

const distPath = path.join(__dirname, '..', 'dist');
const indexPath = path.join(distPath, 'index.html');

// Check if dist folder exists
if (!fs.existsSync(distPath)) {
  console.error('❌ dist folder not found. Run "npm run build" first.');
  process.exit(1);
}

// Check if index.html exists
if (!fs.existsSync(indexPath)) {
  console.error('❌ index.html not found in dist folder.');
  process.exit(1);
}

// Read and analyze index.html
const indexContent = fs.readFileSync(indexPath, 'utf8');
console.log('✅ index.html found');

// Check for script tags
const scriptMatches = indexContent.match(/<script[^>]*src="([^"]*)"[^>]*>/g);
if (scriptMatches) {
  console.log('\n📄 Script tags found:');
  scriptMatches.forEach((script, index) => {
    const srcMatch = script.match(/src="([^"]*)"/);
    if (srcMatch) {
      const src = srcMatch[1];
      console.log(`  ${index + 1}. ${src}`);
      
      // Check if file exists
      const filePath = path.join(distPath, src.replace(/^\//, ''));
      if (fs.existsSync(filePath)) {
        console.log(`     ✅ File exists`);
      } else {
        console.log(`     ❌ File missing: ${filePath}`);
      }
    }
  });
}

// Check for CSS links
const cssMatches = indexContent.match(/<link[^>]*href="([^"]*\.css)"[^>]*>/g);
if (cssMatches) {
  console.log('\n🎨 CSS links found:');
  cssMatches.forEach((link, index) => {
    const hrefMatch = link.match(/href="([^"]*)"/);
    if (hrefMatch) {
      const href = hrefMatch[1];
      console.log(`  ${index + 1}. ${href}`);
      
      // Check if file exists
      const filePath = path.join(distPath, href.replace(/^\//, ''));
      if (fs.existsSync(filePath)) {
        console.log(`     ✅ File exists`);
      } else {
        console.log(`     ❌ File missing: ${filePath}`);
      }
    }
  });
}

// List all files in assets directory
const assetsPath = path.join(distPath, 'assets');
if (fs.existsSync(assetsPath)) {
  console.log('\n📁 Assets directory contents:');
  const files = fs.readdirSync(assetsPath);
  files.forEach(file => {
    console.log(`  - ${file}`);
  });
}

console.log('\n✅ Build check complete!');
console.log('\n💡 Deployment tips:');
console.log('  1. Make sure your server serves static files correctly');
console.log('  2. Configure SPA routing to serve index.html for non-asset routes');
console.log('  3. Set proper MIME types for .js files');
console.log('  4. Check that _redirects file is copied to the build output');

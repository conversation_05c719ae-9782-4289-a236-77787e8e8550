import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addComment, fetchComments } from '../../store/slices/feedSlice';

const PostComments = ({ postId, onClose }) => {
  const dispatch = useDispatch();
  const { comments: allComments, isLoading } = useSelector(state => state.feed);
  const postComments = allComments[postId] || [];
  
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    // Fetch comments only if they are not already loaded for this post
    if (!allComments[postId]) {
      dispatch(fetchComments({ postId }));
    }
  }, [dispatch, postId, allComments]);

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    await dispatch(addComment({ postId, content: newComment }));
    setNewComment('');
  };

  return (
    <div className="mt-6 border-t border-gray-800 pt-6">
      <h4 className="text-lg font-semibold text-white mb-4">Comments</h4>
      
      {/* Add comment form */}
      <form onSubmit={handleSubmitComment} className="flex items-start space-x-3 mb-6">
        <img src="/default-avatar.svg" alt="Your avatar" className="w-8 h-8 rounded-full" />
        <div className="flex-1">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add your comment..."
            className="w-full p-2 border border-gray-700 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 bg-gray-800 text-white"
            rows="2"
          />
          <div className="flex justify-end items-center mt-2">
             <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-white mr-4"
            >
              Cancel
            </button>
            <button 
              type="submit"
              disabled={isLoading}
              className="bg-primary-600 text-white px-4 py-1.5 rounded-lg hover:bg-primary-700 disabled:bg-primary-800 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Posting...' : 'Post'}
            </button>
          </div>
        </div>
      </form>

      {/* List of comments */}
      <div className="space-y-4">
        {isLoading && postComments.length === 0 ? (
          <p className="text-gray-400">Loading comments...</p>
        ) : postComments.length > 0 ? (
          postComments.map(comment => (
            <div key={comment._id} className="flex items-start space-x-3">
              <img src={comment.author.avatar || '/default-avatar.svg'} alt={comment.author.username} className="w-8 h-8 rounded-full" />
              <div className="flex-1 bg-gray-800 rounded-lg p-3">
                <p className="text-sm font-semibold text-white">{comment.author.realName} <span className="text-gray-400">@{comment.author.username}</span></p>
                <p className="text-white mt-1">{comment.content}</p>
              </div>
            </div>
          ))
        ) : (
          <p className="text-gray-400">No comments yet. Be the first to comment!</p>
        )}
      </div>
    </div>
  );
};

export default PostComments; 
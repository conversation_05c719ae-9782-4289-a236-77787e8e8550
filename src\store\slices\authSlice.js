/**
 * Authentication Redux Slice
 *
 * Handles user authentication state and API calls including:
 * - User registration and login
 * - JWT token management
 * - User profile management
 * - Authentication state persistence
 */

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api, { tokenManager, apiHel<PERSON>, API_ENDPOINTS } from '../../utils/api';

/**
 * Async Thunks for Authentication Actions
 */

/**
 * Login user with email and password
 * @param {Object} credentials - User login credentials
 * @param {string} credentials.email - User email
 * @param {string} credentials.password - User password
 */
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.LOGIN, credentials);

      // Extract accessToken from response (matching your backend format)
      const { accessToken, user, message } = response.data;

      if (accessToken) {
        // Store token using centralized token manager
        tokenManager.setToken(accessToken);

        return {
          token: accessToken, // Map accessToken to token for frontend consistency
          user,
          message,
        };
      } else {
        throw new Error('No access token received from server');
      }
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

/**
 * Register new user
 * @param {Object} userData - User registration data
 * @param {string} userData.username - Username
 * @param {string} userData.email - User email
 * @param {string} userData.password - User password
 * @param {string} userData.confirmPassword - Password confirmation
 */
export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      const response = await api.post(API_ENDPOINTS.AUTH.REGISTER, userData);

      return {
        user: response.data.user,
        message: response.data.message,
      };
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

/**
 * Get current user profile
 * Fetches authenticated user's profile information
 */
export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get(API_ENDPOINTS.AUTH.PROFILE);
      return response.data;
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

/**
 * Logout user
 * Clears token and user data
 */
export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { dispatch }) => {
    try {
      // Clear token from storage
      tokenManager.removeToken();

      // Clear any other user-related data if needed
      // Note: Backend doesn't seem to have a logout endpoint, so we just clear local data

      return { message: 'Logged out successfully' };
    } catch (error) {
      // Even if there's an error, we should still clear local data
      tokenManager.removeToken();
      return { message: 'Logged out successfully' };
    }
  }
);

// Note: OTP and password reset functionality commented out as they're not implemented in your backend
// Uncomment and update these when your backend team implements them

/*
export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ email, otp }, { rejectWithValue }) => {
    try {
      const response = await api.post('/auth/verify-otp', { email, otp });
      const { accessToken, user } = response.data;

      if (accessToken) {
        tokenManager.setToken(accessToken);
        return { token: accessToken, user };
      }

      return response.data;
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

export const resendOTP = createAsyncThunk(
  'auth/resendOTP',
  async ({ email }, { rejectWithValue }) => {
    try {
      const response = await api.post('/auth/resend-otp', { email });
      return response.data;
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

export const requestPasswordReset = createAsyncThunk(
  'auth/requestPasswordReset',
  async ({ email }, { rejectWithValue }) => {
    try {
      const response = await api.post('/auth/forgot-password', { email });
      return response.data;
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);

export const resetPassword = createAsyncThunk(
  'auth/resetPassword',
  async ({ token, password }, { rejectWithValue }) => {
    try {
      const response = await api.post('/auth/reset-password', { token, password });
      return response.data;
    } catch (error) {
      return rejectWithValue(apiHelpers.handleError(error));
    }
  }
);
*/

/**
 * Initial Authentication State
 */
const initialState = {
  user: null,
  token: tokenManager.getToken(), // Use centralized token manager
  isAuthenticated: tokenManager.hasToken(), // Set initial auth state based on token presence
  isLoading: false,
  error: null,
  message: null,
  // Note: resetEmailSent removed as password reset not implemented in backend yet
};

/**
 * Authentication Slice Definition
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    /**
     * Clear error message
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Clear success message
     */
    clearMessage: (state) => {
      state.message = null;
    },

    /**
     * Set user credentials manually (for token refresh scenarios)
     */
    setCredentials: (state, action) => {
      const { user, token } = action.payload;
      state.user = user;
      state.token = token;
      state.isAuthenticated = true;
      state.error = null;

      // Update token in storage
      if (token) {
        tokenManager.setToken(token);
      }
    },

    /**
     * Clear all authentication data
     */
    clearAuth: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
      state.message = null;
      tokenManager.removeToken();
    },
  },
  /**
   * Extra Reducers for Async Thunks
   */
  extraReducers: (builder) => {
    builder
      // Login User
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.message = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.message = action.payload.message;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload;
        state.message = null;
      })

      // Register User
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.message = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.message = action.payload.message;
        state.error = null;
        // Note: User is not automatically logged in after registration
        // They need to login separately as per your backend flow
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        state.message = null;
      })

      // Get Current User
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
        // If getting current user fails, user might not be authenticated
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        tokenManager.removeToken();
      })

      // Logout User
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
        state.message = action.payload.message;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        // Even if logout fails, clear the state
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
        state.message = 'Logged out successfully';
      })
      // Note: OTP and password reset reducers commented out as not implemented in backend
      /*
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      */;
  },
});

/**
 * Export action creators
 */
export const {
  clearError,
  clearMessage,
  setCredentials,
  clearAuth
} = authSlice.actions;

/**
 * Export reducer as default
 */
export default authSlice.reducer;

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  DocumentTextIcon, 
  PhotoIcon, 
  LinkIcon, 
  ChartBarIcon 
} from '@heroicons/react/24/outline';

const POST_TYPES = [
  {
    id: 'text',
    name: 'Text Post',
    description: 'Share your thoughts and ideas',
    icon: DocumentTextIcon,
  },
  {
    id: 'image',
    name: 'Image Post',
    description: 'Share photos and images',
    icon: PhotoIcon,
  },
  {
    id: 'link',
    name: 'Link Post',
    description: 'Share interesting links',
    icon: LinkIcon,
  },
  {
    id: 'poll',
    name: 'Poll',
    description: 'Create a poll for the community',
    icon: ChartBarIcon,
  },
];

const PostTypeSelector = ({ onSelect, selectedType }) => {
  const [hoveredType, setHoveredType] = useState(null);

  return (
    <div className="grid grid-cols-2 gap-4">
      {POST_TYPES.map((type) => {
        const Icon = type.icon;
        const isSelected = selectedType === type.id;
        const isHovered = hoveredType === type.id;

        return (
          <motion.button
            key={type.id}
            className={`relative p-4 rounded-lg border-2 transition-colors ${
              isSelected
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-200 dark:border-dark-700 hover:border-primary-300 dark:hover:border-primary-700'
            }`}
            onClick={() => onSelect(type.id)}
            onHoverStart={() => setHoveredType(type.id)}
            onHoverEnd={() => setHoveredType(null)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center space-x-3">
              <div
                className={`p-2 rounded-lg ${
                  isSelected
                    ? 'bg-primary-100 dark:bg-primary-800'
                    : 'bg-gray-100 dark:bg-dark-700'
                }`}
              >
                <Icon
                  className={`h-6 w-6 ${
                    isSelected
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-600 dark:text-gray-400'
                  }`}
                />
              </div>
              <div className="text-left">
                <h3
                  className={`font-medium ${
                    isSelected
                      ? 'text-primary-900 dark:text-primary-100'
                      : 'text-gray-900 dark:text-white'
                  }`}
                >
                  {type.name}
                </h3>
                <p
                  className={`text-sm ${
                    isSelected
                      ? 'text-primary-700 dark:text-primary-300'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}
                >
                  {type.description}
                </p>
              </div>
            </div>

            {isSelected && (
              <motion.div
                className="absolute inset-0 border-2 border-primary-500 rounded-lg"
                layoutId="selectedType"
                transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
              />
            )}
          </motion.button>
        );
      })}
    </div>
  );
};

export default PostTypeSelector; 
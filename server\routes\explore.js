const express = require('express');
const router = express.Router();
const Post = require('../models/Post');
const User = require('../models/User');
const Community = require('../models/Community');
const auth = require('../middleware/auth');

// @route   GET api/explore/trending
// @desc    Get trending topics
// @access  Public
router.get('/trending', async (req, res) => {
  try {
    // Get posts from the last 7 days
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // Aggregate posts by tags and count occurrences
    const trendingTopics = await Post.aggregate([
      {
        $match: {
          createdAt: { $gte: oneWeekAgo },
          tags: { $exists: true, $ne: [] }
        }
      },
      { $unwind: '$tags' },
      {
        $group: {
          _id: '$tags',
          count: { $sum: 1 },
          posts: { $push: '$_id' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Format the response
    const formattedTopics = trendingTopics.map(topic => ({
      id: topic._id,
      title: topic._id,
      posts: topic.count,
      image: `/api/images/topics/${topic._id.toLowerCase().replace(/\s+/g, '-')}.jpg`
    }));

    res.json(formattedTopics);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/explore/suggested-people
// @desc    Get suggested people to follow
// @access  Private
router.get('/suggested-people', auth, async (req, res) => {
  try {
    // Get users that the current user is not following
    const currentUser = await User.findById(req.user.id);
    const following = currentUser.following || [];

    const suggestedPeople = await User.find({
      _id: { $nin: [...following, req.user.id] }
    })
      .select('name avatar bio followers')
      .sort({ followers: -1 })
      .limit(10);

    // Format the response
    const formattedPeople = suggestedPeople.map(person => ({
      id: person._id,
      name: person.name,
      avatar: person.avatar,
      bio: person.bio,
      followers: person.followers.length
    }));

    res.json(formattedPeople);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router; 
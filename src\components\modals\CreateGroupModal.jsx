import { useState } from 'react';
import Select from 'react-select';
import { motion } from 'framer-motion';
import toast from 'react-hot-toast';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const mockUsers = [
  { value: '1', label: '<PERSON>' },
  { value: '2', label: '<PERSON>' },
  { value: '3', label: '<PERSON>' },
  { value: '4', label: '<PERSON>' },
];

const customStyles = {
  control: (provided) => ({
    ...provided,
    backgroundColor: '#1f293b',
    borderColor: '#4b5563',
  }),
  menu: (provided) => ({
    ...provided,
    backgroundColor: '#1f293b',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected ? '#3b82f6' : '#1f293b',
    '&:hover': {
      backgroundColor: '#374151',
    },
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: '#374151',
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: '#ffffff',
  }),
};

const CreateGroupModal = ({ onClose }) => {
  const [groupName, setGroupName] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);

  const handleCreateGroup = () => {
    if (groupName.trim() && selectedUsers.length > 0) {
      toast.success('Group chat feature is coming soon!');
      onClose();
    } else {
      toast.error('Please provide a group name and select participants.');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/60 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 50, opacity: 0 }}
        className="bg-background rounded-lg shadow-xl p-8 w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        <h2 className="text-2xl font-bold text-white mb-6">Create New Group</h2>
        
        <div className="space-y-6">
          <div>
            <label htmlFor="groupName" className="block text-sm font-medium text-gray-300 mb-2">
              Group Name
            </label>
            <Input
              id="groupName"
              type="text"
              placeholder="Enter a name for your group"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Participants
            </label>
            <Select
              isMulti
              options={mockUsers}
              value={selectedUsers}
              onChange={setSelectedUsers}
              styles={customStyles}
              placeholder="Select members..."
            />
          </div>
        </div>

        <div className="mt-8 flex justify-end space-x-4">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleCreateGroup}>
            Create Group
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default CreateGroupModal; 
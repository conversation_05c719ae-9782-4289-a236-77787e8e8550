import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { deleteCommunity } from '../../store/slices/communitySlice';
import { Button, Input } from '../ui';
import { TrashIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

const DeleteCommunity = ({ community, onClose }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [confirmation, setConfirmation] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleDelete = async () => {
    if (confirmation !== community.name) {
      toast.error('Please type the community name to confirm deletion');
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(deleteCommunity(community.slug)).unwrap();
      toast.success('Community deleted successfully');
      navigate('/communities');
    } catch (error) {
      toast.error(error.message || 'Failed to delete community');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Delete Community
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <div className="space-y-4">
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Warning
              </h3>
              <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>
                  This action cannot be undone. This will permanently delete the
                  community and all its content, including posts, comments, and
                  member data.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            To confirm, type the community name: <span className="font-bold">{community.name}</span>
          </label>
          <Input
            type="text"
            value={confirmation}
            onChange={(e) => setConfirmation(e.target.value)}
            placeholder="Type community name to confirm"
            className="w-full"
          />
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDelete}
            loading={isSubmitting}
            disabled={confirmation !== community.name}
          >
            <TrashIcon className="h-5 w-5 inline mr-2" />
            Delete Community
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DeleteCommunity; 
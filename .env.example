# Environment Variables Example
# Copy this file to .env and update the values according to your setup

# API Configuration
# Backend server configuration - should match your backend setup
VITE_API_BASE_URL=http://localhost:3000/api

# Development Configuration
VITE_NODE_ENV=development

# Production Configuration (update for production deployment)
# VITE_API_BASE_URL=https://your-backend-domain.com/api
# VITE_NODE_ENV=production

# Security Configuration
VITE_TOKEN_STORAGE_KEY=accessToken
VITE_TOKEN_EXPIRY_BUFFER=300000

# Feature Flags (for future use)
VITE_ENABLE_SOCKET=true
VITE_ENABLE_PWA=true

# Instructions:
# 1. Copy this file to .env in the same directory
# 2. Update VITE_API_BASE_URL to match your backend server URL
# 3. For production, update the API URL to your deployed backend
# 4. Never commit the .env file to version control

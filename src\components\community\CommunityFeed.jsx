import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchCommunityPosts } from '../../store/slices/postSlice';
import { Button } from '../ui';
import { motion } from 'framer-motion';

const CommunityFeed = ({ communityId, canPost }) => {
  const dispatch = useDispatch();
  const { posts, isLoading, totalPages, currentPage } = useSelector((state) => state.post);
  const { user } = useSelector((state) => state.auth);
  const [page, setPage] = useState(1);

  useEffect(() => {
    if (communityId) {
      dispatch(fetchCommunityPosts({ communityId, page: 1 }));
      setPage(1);
    }
  }, [dispatch, communityId]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop < document.documentElement.offsetHeight - 500 || isLoading) {
        return;
      }
      if (currentPage < totalPages) {
        setPage(prevPage => prevPage + 1);
      } else {
        // Recycle posts
        setPage(1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isLoading, currentPage, totalPages]);

  useEffect(() => {
    if (page > 1 && communityId) {
      dispatch(fetchCommunityPosts({ communityId, page }));
    }
  }, [dispatch, page, communityId]);

  const handleFollowToggle = (authorId) => {
    // Mock logic for follow/unfollow
    console.log(`Toggling follow for author ${authorId}`);
    // In a real app, you would dispatch an action here
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-gray-800 rounded-lg shadow-sm p-4 animate-pulse h-32" />
        ))}
      </div>
    );
  }

  if (!posts || posts.length === 0) {
    return <div className="text-center text-gray-400 py-8">No posts yet.</div>;
  }

  return (
    <div className="space-y-6">
      {posts.map((post) => (
        <motion.div
          key={post._id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-background rounded-lg shadow-sm p-4"
        >
          <div className="flex items-center mb-2">
            <img src={post.author.avatar} alt={post.author.username} className="h-8 w-8 rounded-full mr-2" />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <p className="font-medium text-white">{post.author.realName}</p>
                <p className="text-xs text-gray-400">@{post.author.username}</p>
              </div>
              <p className="text-xs text-gray-500">{new Date(post.createdAt).toLocaleString()}</p>
            </div>
            {user._id !== post.author._id && (
              <Button
                variant={post.author.isFollowed ? 'secondary' : 'primary'}
                onClick={() => handleFollowToggle(post.author._id)}
                size="sm"
              >
                {post.author.isFollowed ? 'Following' : 'Follow'}
              </Button>
            )}
          </div>
          <div className="text-gray-200 mb-2">{post.content}</div>
          {post.image && <img src={post.image} alt="Post" className="rounded-lg max-h-64 object-cover" />}
        </motion.div>
      ))}
    </div>
  );
};

export default CommunityFeed; 
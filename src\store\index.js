import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import communityReducer from './slices/communitySlice';
import postReducer from './slices/postSlice';
import feedReducer from './slices/feedSlice';
import notificationReducer from './slices/notificationSlice';
import chatReducer from './slices/chatSlice';
import profileReducer from './slices/profileSlice';
import exploreReducer from './slices/exploreSlice';
import uiReducer from './slices/uiSlice';
import categoryReducer from './slices/categorySlice';
import featureReducer from './slices/featureSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    communities: communityReducer,
    posts: postReducer,
    feed: feedReducer,
    notifications: notificationReducer,
    chat: chatReducer,
    profile: profileReducer,
    explore: exploreReducer,
    ui: uiReducer,
    categories: categoryReducer,
    features: featureReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Make store available globally for API interceptors
if (typeof window !== 'undefined') {
  window.__REDUX_STORE__ = store;
}



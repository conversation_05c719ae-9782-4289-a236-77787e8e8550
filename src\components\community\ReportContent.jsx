import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { toast } from 'react-hot-toast';
import { reportContent } from '../../store/slices/communitySlice';
import { Button, Textarea } from '../ui';
import { FlagIcon } from '@heroicons/react/24/outline';

const REPORT_TYPES = {
  post: 'Post',
  user: 'User',
  comment: 'Comment',
};

const REPORT_REASONS = [
  'Spam',
  'Harassment',
  'Hate Speech',
  'Violence',
  'Inappropriate Content',
  'Other',
];

const ReportContent = ({ slug, contentId, type, onClose }) => {
  const dispatch = useDispatch();
  const [reason, setReason] = useState('');
  const [selectedReason, setSelectedReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedReason && !reason.trim()) {
      toast.error('Please provide a reason for reporting');
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        reportContent({
          slug,
          contentId,
          type,
          reason: selectedReason === 'Other' ? reason : selectedReason,
        })
      ).unwrap();
      toast.success('Report submitted successfully');
      onClose();
    } catch (error) {
      toast.error(error.message || 'Failed to submit report');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Report {REPORT_TYPES[type]}
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
        >
          ×
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Reason for Report
          </label>
          <div className="grid grid-cols-2 gap-2">
            {REPORT_REASONS.map((r) => (
              <button
                key={r}
                type="button"
                className={`px-3 py-2 text-sm rounded-md ${
                  selectedReason === r
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-dark-700 dark:text-gray-300 dark:hover:bg-dark-600'
                }`}
                onClick={() => setSelectedReason(r)}
              >
                {r}
              </button>
            ))}
          </div>
        </div>

        {selectedReason === 'Other' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Please specify
            </label>
            <Textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Describe the issue..."
              rows={3}
            />
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" loading={isSubmitting}>
            <FlagIcon className="h-5 w-5 inline mr-2" />
            Submit Report
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ReportContent; 
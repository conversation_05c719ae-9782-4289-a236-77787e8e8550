import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchFeed, setFeedType } from '../store/slices/feedSlice';
import { Button } from '../components/ui/Button';
import { HeartIcon, ChatBubbleLeftIcon, ShareIcon, BookmarkIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid';
import PostComments from '../components/posts/PostComments';

const FeedPage = () => {
  const dispatch = useDispatch();
  const { posts, isLoading, feedType, hasMore } = useSelector((state) => state.feed);
  const { user } = useSelector((state) => state.auth);

  const [page, setPage] = useState(1);
  const [selectedTab, setSelectedTab] = useState('all');
  const [viewingCommentsForPostId, setViewingCommentsForPostId] = useState(null);

  useEffect(() => {
    dispatch(fetchFeed({ page: 1, type: selectedTab }));
    setPage(1);
  }, [dispatch, selectedTab]);

  const handleTabChange = (tab) => {
    setSelectedTab(tab);
    dispatch(setFeedType(tab));
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop < document.documentElement.offsetHeight - 500 || isLoading) {
        return;
      }
      if (hasMore) {
        setPage(prevPage => prevPage + 1);
      } else {
        // Recycle posts
        setPage(1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isLoading, hasMore]);

  useEffect(() => {
    if (page > 1) {
      dispatch(fetchFeed({ page, type: selectedTab }));
    }
  }, [dispatch, page, selectedTab]);

  const handleFollowToggle = (authorId) => {
    // Mock logic for follow/unfollow
    console.log(`Toggling follow for author ${authorId}`);
    // In a real app, you would dispatch an action here
  };

  const handleViewComments = (postId) => {
    setViewingCommentsForPostId(postId);
  };

  const feedTabs = [
    { id: 'all', label: 'For You', description: 'Personalized content' },
    { id: 'following', label: 'Following', description: 'From people you follow' },
    { id: 'trending', label: 'Trending', description: 'Popular right now' },
    { id: 'communities', label: 'Communities', description: 'From your communities' },
  ];

  return (
    <div className="max-w-3xl mx-auto p-4">
      {/* Welcome message */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-white mb-2">
          Welcome back, {user?.realName}!
        </h1>
        <p className="text-gray-400">
          Discover what's happening in your network
        </p>
      </motion.div>

      {/* Feed tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-800">
          <nav className="-mb-px flex space-x-8">
            {feedTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`${
                  selectedTab === tab.id
                    ? 'border-primary-500 text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Create post section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-background rounded-lg shadow-sm border border-gray-800 p-6 mb-6"
      >
        <div className="flex items-start space-x-4">
          <img
            src={user?.avatar || '/default-avatar.png'}
            alt={user?.username}
            className="w-12 h-12 rounded-full"
          />
          <div className="flex-1">
            <textarea
              placeholder="What's on your mind?"
              className="w-full p-3 border border-gray-700 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 bg-gray-800 text-white"
              rows="3"
            />
            <div className="flex items-center justify-between mt-4">
              <div className="flex space-x-4">
                <button className="text-gray-500 hover:text-primary-600 transition-colors">
                  📷 Photo
                </button>
                <button className="text-gray-500 hover:text-primary-600 transition-colors">
                  🎥 Video
                </button>
                <button className="text-gray-500 hover:text-primary-600 transition-colors">
                  📊 Poll
                </button>
              </div>
              <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                Post
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Feed content */}
      <div className="space-y-6">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="bg-background rounded-lg shadow-sm border border-gray-800 p-6"
            >
              <div className="animate-pulse">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-gray-800 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-800 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-800 rounded w-1/6"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-800 rounded"></div>
                  <div className="h-4 bg-gray-800 rounded w-3/4"></div>
                </div>
                <div className="h-48 bg-gray-800 rounded-lg mt-4"></div>
              </div>
            </div>
          ))
        ) : posts && posts.length > 0 ? (
          posts.map((post, index) => (
            <motion.div
              key={post._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-background rounded-lg shadow-sm border border-gray-800 p-6"
            >
              {/* Post header */}
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={post.author?.avatar || '/default-avatar.png'}
                  alt={post.author?.username}
                  className="w-12 h-12 rounded-full"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-white">
                      {post.author?.realName || 'Anonymous User'}
                    </h3>
                    <p className="text-sm text-gray-400">
                      @{post.author?.username || 'anonymous'}
                    </p>
                  </div>
                  <p className="text-sm text-gray-500">
                    {new Date(post.createdAt).toLocaleDateString()}
                  </p>
                </div>
                {user._id !== post.author._id && (
                  <Button
                    variant={post.author.isFollowed ? 'secondary' : 'primary'}
                    onClick={() => handleFollowToggle(post.author._id)}
                    size="sm"
                  >
                    {post.author.isFollowed ? 'Following' : 'Follow'}
                  </Button>
                )}
                <button className="text-gray-400 hover:text-gray-300">
                  ⋯
                </button>
              </div>

              {/* Post content */}
              <div className="mb-4">
                <p className="text-white mb-4">
                  {post.content}
                </p>
                
                {post.image && (
                  <img
                    src={post.image}
                    alt="Post content"
                    className="w-full h-64 object-cover rounded-lg"
                  />
                )}
              </div>

              {/* Post actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-800">
                <div className="flex space-x-6">
                  <button className="flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors">
                    {post.isLiked ? (
                      <HeartIconSolid className="h-5 w-5 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5" />
                    )}
                    <span>{post.likesCount}</span>
                  </button>
                  <button 
                    onClick={() => handleViewComments(post._id)}
                    className="flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors"
                  >
                    <ChatBubbleLeftIcon className="h-5 w-5" />
                    <span>{post.commentsCount}</span>
                  </button>
                  <button className="flex items-center space-x-2 text-gray-500 hover:text-green-500 transition-colors">
                    <ShareIcon className="h-5 w-5" />
                    <span>{post.sharesCount}</span>
                  </button>
                </div>
                <button className="text-gray-500 hover:text-gray-300 transition-colors">
                  <BookmarkIcon className="h-5 w-5" />
                </button>
              </div>
              {viewingCommentsForPostId === post._id && (
                <PostComments 
                  postId={post._id} 
                  onClose={() => setViewingCommentsForPostId(null)} 
                />
              )}
            </motion.div>
          ))
        ) : (
          // Empty state
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12"
          >
            <div className="text-6xl mb-4">📱</div>
            <h3 className="text-xl font-semibold text-white mb-2">
              No posts yet
            </h3>
            <p className="text-gray-400 mb-6">
              Start following people or join communities to see posts in your feed
            </p>
            <Button onClick={() => window.location.href = '/explore'}>
              Discover People
            </Button>
          </motion.div>
        )}
      </div>

      {/* Pagination loader */}
      {isLoading && page > 1 && (
        <div className="text-center py-4">
          <p className="text-gray-400">Loading more posts...</p>
        </div>
      )}
    </div>
  );
};

export default FeedPage;

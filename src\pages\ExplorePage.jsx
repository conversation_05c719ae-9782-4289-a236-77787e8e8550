import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { fetchTrendingTopics } from '../store/slices/exploreSlice';

const ExplorePage = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { trendingTopics, status, error } = useSelector((state) => state.explore);
  
  useEffect(() => {
    if (status === 'idle') {
      dispatch(fetchTrendingTopics());
    }
  }, [dispatch, status]);

  // Loading state
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="text-center max-w-md">
          <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-4">
            Failed to load explore data
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">{error}</p>
          <button
            onClick={() => dispatch(fetchTrendingTopics())}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Explore</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Discover trending topics and communities
          </p>
        </motion.div>

        {/* Trending Section */}
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {isAuthenticated ? 'Trending for you' : 'Trending now'}
            </h2>
            {!isAuthenticated && (
              <Link
                to="/register"
                className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                Sign up to see more
              </Link>
            )}
          </div>

          {trendingTopics?.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {trendingTopics.map((topic, index) => (
                <motion.div
                  key={topic.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                >
                  <Link to={`/topic/${topic.id}`} className="block">
                    <div className="p-6">
                      <div className="flex items-center space-x-4">
                        {topic.image && (
                          <img
                            src={topic.image}
                            alt={topic.title}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                        )}
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {topic.title}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {topic.postCount} posts
                          </p>
                        </div>
                      </div>
                      {topic.description && (
                        <p className="mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                          {topic.description}
                        </p>
                      )}
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                No trending topics found. Check back later!
              </p>
            </div>
          )}
        </section>

        {/* Recommended Communities Section (for authenticated users) */}
        {isAuthenticated && (
          <section className="mb-12">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              Recommended Communities
            </h2>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                Community recommendations coming soon!
              </p>
            </div>
          </section>
        )}

        {/* CTA for Unauthenticated Users */}
        {!isAuthenticated && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-8 text-center"
          >
            <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
              Ready to dive deeper?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
              Join our community to personalize your explore experience and connect with others.
            </p>
            <div className="flex justify-center space-x-4">
              <Link
                to="/register"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Sign Up
              </Link>
              <Link
                to="/login"
                className="px-6 py-2 border border-blue-600 text-blue-600 dark:text-blue-400 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
              >
                Log In
              </Link>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ExplorePage;
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  GlobeAltIcon as CompassIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Feed', href: '/feed', icon: HomeIcon },
  { name: 'Messages', href: '/messages', icon: ChatBubbleLeftRightIcon },
  { name: 'Communities', href: '/communities', icon: UserGroupIcon },
  { name: 'Explore', href: '/explore', icon: CompassIcon },
  { name: 'Profile', href: '/profile', icon: UserCircleIcon },
];

const MobileNav = () => {
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const { unreadCounts } = useSelector((state) => state.chat);

  const totalUnread = Object.values(unreadCounts || {}).reduce((sum, count) => sum + count, 0);

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 lg:hidden">
      <div className="grid grid-cols-5 h-16">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href || 
            (item.href === '/profile' && location.pathname.startsWith('/profile'));
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex flex-col items-center justify-center space-y-1 ${
                isActive
                  ? 'text-primary-600 dark:text-primary-400'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              <div className="relative">
                <item.icon className="h-6 w-6" />
                {item.name === 'Messages' && totalUnread > 0 && (
                  <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                    {totalUnread > 99 ? '99+' : totalUnread}
                  </span>
                )}
              </div>
              <span className="text-xs">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default MobileNav; 
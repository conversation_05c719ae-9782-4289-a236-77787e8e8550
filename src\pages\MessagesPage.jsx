import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AnimatePresence } from 'framer-motion';
import { fetchChats, setActiveChat, sendMessage } from '../store/slices/chatSlice';
import { Input } from '../components/ui/Input';
import { Button } from '../components/ui/Button';
import { MagnifyingGlassIcon, PlusIcon } from '@heroicons/react/24/outline';
import CreateGroupModal from '../components/modals/CreateGroupModal';

const MessagesPage = () => {
  const dispatch = useDispatch();
  const { chats, activeChat, isLoading } = useSelector((state) => state.chat);
  const { user } = useSelector((state) => state.auth);

  const [searchQuery, setSearchQuery] = useState('');
  const [messageInput, setMessageInput] = useState('');
  // Formal Chat Mode state
  const [formalMode, setFormalMode] = useState(false); // Toggle for Formal Mode
  const [formalDelay, setFormalDelay] = useState(3); // User-defined preview seconds (default 3)
  const [pendingMessage, setPendingMessage] = useState(null); // Holds message in preview
  // const [pendingTimer, setPendingTimer] = useState(null); // Timer ID (removed: unused variable)
  const [remaining, setRemaining] = useState(0); // Remaining preview seconds

  // One-Time Message state (frontend-only, in-memory)
  const [oneTimeChecked, setOneTimeChecked] = useState(false); // Checkbox for marking as one-time
  const [viewedOneTimeIds, setViewedOneTimeIds] = useState(new Set()); // Set of message IDs viewed


  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);

  useEffect(() => {
    dispatch(fetchChats());
  }, [dispatch]);

  const filteredChats = chats.filter(chat =>
    chat.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.participants?.some(p => 
      p.username?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleChatSelect = (chat) => {
    dispatch(setActiveChat(chat));
  };

  // Handles sending logic for both normal and formal mode
  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!messageInput.trim() || !activeChat) return;
    if (!formalMode) {
      // Normal mode: send immediately
      try {
        await dispatch(sendMessage({
          chatId: activeChat._id,
          content: messageInput,
          oneTime: oneTimeChecked // Attach oneTime flag in message object (UI only)
        })).unwrap();
        setMessageInput('');
        setOneTimeChecked(false);
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    } else {
      // Formal mode: enter preview state
      setPendingMessage(messageInput);
      setRemaining(formalDelay);
      setMessageInput('');
    }
  };

  // Effect for formal mode preview timer
  useEffect(() => {
    if (pendingMessage === null) return;
    if (remaining <= 0) {
      // Timer done: send message
      (async () => {
        try {
          await dispatch(sendMessage({
            chatId: activeChat._id,
            content: pendingMessage
          })).unwrap();
        } catch (error) {
          console.error('Failed to send message:', error);
        }
        setPendingMessage(null);
        
      })();
      return;
    }
    // Start countdown
    const timer = setTimeout(() => setRemaining(r => r - 1), 1000);
    // setPendingTimer(timer); // removed: unused variable
    return () => clearTimeout(timer);
  }, [pendingMessage, remaining, activeChat, dispatch]);

  // Cancel pending message
  const handleCancelPending = () => {
    setPendingMessage(null);
    
    setRemaining(0);
  };

  // Edit pending message
  const handleEditPending = () => {
    setMessageInput(pendingMessage);
    setPendingMessage(null);
    
    setRemaining(0);
  };


  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-12 bg-gray-800 rounded-lg mb-4"></div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-800 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 h-[calc(100vh-8rem)]">
        {/* Chat List */}
        <div className={`
          ${activeChat ? 'hidden lg:block' : 'block'}
          lg:col-span-4 bg-background rounded-lg shadow-sm overflow-hidden
        `}>
          <div className="p-4 border-b border-gray-800">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="Search chats..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              </div>
              <Button onClick={() => setShowCreateGroupModal(true)} size="icon">
                <PlusIcon className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="overflow-y-auto h-[calc(100%-4rem)]">
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <button
                  key={chat._id}
                  onClick={() => handleChatSelect(chat)}
                  className={`w-full p-4 hover:bg-gray-900 transition-colors ${
                    activeChat?._id === chat._id ? 'bg-gray-900' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <img
                      src={chat.type === 'direct' ? chat.participants[0].avatar : chat.image}
                      alt={chat.name}
                      className="w-12 h-12 rounded-full"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-white truncate">
                          {chat.name}
                        </h3>
                        {chat.lastMessage && (
                          <span className="text-sm text-gray-400">
                            {new Date(chat.lastMessage.createdAt).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-400 truncate">
                          {chat.lastMessage?.content || 'No messages yet'}
                        </p>
                        {chat.unreadCount > 0 && (
                          <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                            {chat.unreadCount}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </button>
              ))
            ) : (
              <div className="text-center py-8 text-gray-400">
                No chats found
              </div>
            )}
          </div>
        </div>

        {/* Chat Window */}
        <div className={`
          ${activeChat ? 'block' : 'hidden lg:block'}
          lg:col-span-8 bg-background rounded-lg shadow-sm overflow-hidden flex flex-col
        `}>
          {activeChat ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-800">
                <div className="flex items-center space-x-3">
                  <button
                    className="lg:hidden mr-2 p-2 rounded-full hover:bg-gray-800"
                    onClick={() => dispatch(setActiveChat(null))}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                  </button>
                  <img
                    src={activeChat.type === 'direct' ? activeChat.participants[0].avatar : activeChat.image}
                    alt={activeChat.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <h2 className="font-medium text-white">
                      {activeChat.name}
                    </h2>
                    <p className="text-sm text-gray-400">
                      {activeChat.type === 'direct' ? 'Direct Message' : `${activeChat.participants.length} members`}
                    </p>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {activeChat.messages?.map((message) => (
                  <div
                    key={message._id}
                    className={`flex flex-col ${message.senderId === user._id ? 'items-end' : 'items-start'}`}
                  >
                    {activeChat.type === 'group' && message.senderId !== user._id && (
                      <p className="text-xs text-gray-400 mb-1 ml-2">
                        {message.sender?.realName || message.sender?.username}
                      </p>
                    )}
                    <div
                      className={`max-w-[70%] rounded-lg p-3 ${
                        message.senderId === user._id
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-800 text-white'
                      }`}
                    >
                      {/* One-Time Message UI logic (frontend-only) */}
                      {message.oneTime && !viewedOneTimeIds.has(message._id) ? (
                        <button
                          className="w-full flex flex-col items-center justify-center p-4 bg-gray-900/80 rounded-lg border-2 border-dashed border-yellow-500 text-yellow-100 blur-sm hover:blur-none hover:bg-gray-900/90 transition-all focus:blur-none focus:bg-gray-900/90 outline-none"
                          style={{ filter: 'blur(4px)', position: 'relative' }}
                          onClick={e => {
                            e.preventDefault();
                            setViewedOneTimeIds(prev => new Set([...prev, message._id]));
                          }}
                          tabIndex={0}
                          aria-label="Reveal one-time message"
                        >
                          <span className="block text-lg font-semibold text-yellow-200 mb-1">
                            One-Time Message
                          </span>
                          <span className="block text-xs text-yellow-300">Click to view (will disappear)</span>
                        </button>
                      ) : message.oneTime && viewedOneTimeIds.has(message._id) ? (
                        <div className="w-full flex flex-col items-center justify-center p-4 bg-gray-900/80 rounded-lg border border-gray-700 text-gray-400 italic opacity-60 select-none">
                          <span className="block text-xs">This one-time message has already been viewed.</span>
                        </div>
                      ) : (
                        // Normal message rendering
                        <>{message.content}</>
                      )}
                      <span className="text-xs opacity-75 mt-1 block">
                        {new Date(message.createdAt).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              {/* Formal Mode Toggle & Delay */}
              <div className="flex items-center space-x-3 p-2">
                <label className="flex items-center cursor-pointer select-none">
                  <input
                    type="checkbox"
                    checked={formalMode}
                    onChange={e => setFormalMode(e.target.checked)}
                    className="mr-2 accent-primary-600"
                  />
                  <span className="text-sm text-white">Formal Mode</span>
                </label>
                {formalMode && (
                  <>
                    <span className="text-xs text-gray-400">Delay:</span>
                    <input
                      type="number"
                      min={1}
                      max={10}
                      value={formalDelay}
                      onChange={e => setFormalDelay(Math.max(1, Math.min(10, Number(e.target.value))))}
                      className="w-12 px-1 py-0.5 rounded bg-gray-900 text-white border border-gray-700 text-xs"
                      title="Preview seconds"
                    />
                    <span className="text-xs text-gray-400">sec</span>
                  </>
                )}
              </div>

              {/* Pending Message Preview */}
              {pendingMessage && (
                <div className="p-3 mb-2 rounded-lg bg-yellow-900/80 border border-yellow-600 flex items-center justify-between animate-pulse">
                  <div className="flex-1 min-w-0">
                    <span className="block text-yellow-200 font-medium">Preview:</span>
                    <span className="block text-yellow-100 break-words">{pendingMessage}</span>
                    <span className="block text-xs text-yellow-300 mt-1">Sending in {remaining}s...</span>
                  </div>
                  <div className="flex flex-col space-y-1 ml-4">
                    <button
                      type="button"
                      className="text-xs bg-yellow-700 hover:bg-yellow-800 rounded px-2 py-1 text-white"
                      onClick={handleEditPending}
                    >Edit</button>
                    <button
                      type="button"
                      className="text-xs bg-gray-700 hover:bg-gray-800 rounded px-2 py-1 text-white"
                      onClick={handleCancelPending}
                    >Cancel</button>
                  </div>
                </div>
              )}

              <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-800">
                <div className="flex flex-col gap-2">
                  <div className="flex space-x-2">
                    <Input
                      type="text"
                      placeholder="Type a message..."
                      value={messageInput}
                      onChange={(e) => setMessageInput(e.target.value)}
                      className="flex-1"
                      disabled={!!pendingMessage}
                    />
                    <button
                      type="submit"
                      disabled={!messageInput.trim() || !!pendingMessage}
                      className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {formalMode ? 'Preview' : 'Send'}
                    </button>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="one-time-msg"
                      checked={oneTimeChecked}
                      onChange={e => setOneTimeChecked(e.target.checked)}
                      disabled={!!pendingMessage}
                    />
                    <label htmlFor="one-time-msg" className="text-xs text-white cursor-pointer select-none">
                      One-Time Message
                    </label>
                  </div>
                </div>
              </form> 
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl mb-4">💬</div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Select a chat
                </h3>
                <p className="text-gray-400">
                  Choose a conversation from the list to start messaging
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
      <AnimatePresence>
        {showCreateGroupModal && (
          <CreateGroupModal onClose={() => setShowCreateGroupModal(false)} />
        )}
      </AnimatePresence>
    </div>
  );
};

export default MessagesPage;

import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { createPost } from '../../store/slices/communitySlice';
import { Button } from '../ui';
import PostTypeSelector from './PostTypeSelector';
import TextPost from './post-types/TextPost';
import ImagePost from './post-types/ImagePost';
import LinkPost from './post-types/LinkPost';
import PollPost from './post-types/PollPost';
import RuleViolationWarning from './RuleViolationWarning';
import { checkContent } from '../../utils/contentFilter';

const CreatePost = ({ communityId, onSuccess }) => {
  const dispatch = useDispatch();
  const [postType, setPostType] = useState(null);
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [violations, setViolations] = useState([]);

  const handleContentChange = (newContent) => {
    setContent(newContent);
    // Check for violations
    const contentViolations = checkContent(
      typeof newContent === 'string' ? newContent : JSON.stringify(newContent),
      postType
    );
    setViolations(contentViolations);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (violations.length > 0) {
      toast.error('Please fix the content violations before posting');
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        createPost({
          communityId,
          type: postType,
          content,
        })
      ).unwrap();
      
      toast.success('Post created successfully');
      setPostType(null);
      setContent('');
      setViolations([]);
      if (onSuccess) onSuccess();
    } catch (error) {
      toast.error(error.message || 'Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderPostTypeComponent = () => {
    switch (postType) {
      case 'text':
        return <TextPost value={content} onChange={handleContentChange} />;
      case 'image':
        return <ImagePost value={content} onChange={handleContentChange} />;
      case 'link':
        return <LinkPost value={content} onChange={handleContentChange} />;
      case 'poll':
        return <PollPost value={content} onChange={handleContentChange} />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
      <AnimatePresence mode="wait">
        {!postType ? (
          <motion.div
            key="selector"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Create a Post
            </h2>
            <PostTypeSelector
              selectedType={postType}
              onSelect={setPostType}
            />
          </motion.div>
        ) : (
          <motion.form
            key="form"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onSubmit={handleSubmit}
            className="space-y-4"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Create {postType.charAt(0).toUpperCase() + postType.slice(1)} Post
              </h2>
              <button
                type="button"
                onClick={() => {
                  setPostType(null);
                  setContent('');
                  setViolations([]);
                }}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                ×
              </button>
            </div>

            {renderPostTypeComponent()}

            <AnimatePresence>
              {violations.map((violation, index) => (
                <RuleViolationWarning
                  key={index}
                  violation={violation}
                  onDismiss={() => {
                    setViolations(violations.filter((_, i) => i !== index));
                  }}
                  onAppeal={() => {
                    // Implement appeal logic
                    toast.error('Appeal feature coming soon');
                  }}
                />
              ))}
            </AnimatePresence>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  setPostType(null);
                  setContent('');
                  setViolations([]);
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={!content || violations.length > 0}
              >
                Post
              </Button>
            </div>
          </motion.form>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CreatePost; 
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { AnimatePresence } from 'framer-motion';
// Pages
import LandingPage from './pages/LandingPage';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import OTPVerificationPage from './pages/auth/OTPVerificationPage';
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage';
import ResetPasswordPage from './pages/auth/ResetPasswordPage';
import FeedPage from './pages/FeedPage';
import MessagesPage from './pages/MessagesPage';
import CommunitiesPage from './pages/CommunitiesPage';
import ExplorePage from './pages/ExplorePage'; 
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import FollowersPage from './pages/FollowersPage';
import FollowingPage from './pages/FollowingPage';
// Components
import ProtectedRoute from './components/navigation/ProtectedRoute';
import Layout from './components/layouts/Layout';

const App = () => {
  const { isAuthenticated } = useSelector((state) => state.auth);

  return (
    <Router>
      <AnimatePresence mode="wait">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route
            path="/login"
            element={!isAuthenticated ? <LoginPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/register"
            element={!isAuthenticated ? <RegisterPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/verify-otp"
            element={!isAuthenticated ? <OTPVerificationPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/forgot-password"
            element={!isAuthenticated ? <ForgotPasswordPage /> : <Navigate to="/feed" />}
          />
          <Route
            path="/reset-password"
            element={!isAuthenticated ? <ResetPasswordPage /> : <Navigate to="/feed" />}
          />
          
          {/* Explore Route - Accessible to both authenticated and unauthenticated users */}
          <Route path="/explore" element={<ExplorePage />} />
          
          {/* Protected Routes */}
          <Route element={<ProtectedRoute />}>
            <Route element={<Layout />}> 
              <Route path="/feed" element={<FeedPage />} />
              <Route path="/messages" element={<MessagesPage />} />
              <Route path="/communities" element={<CommunitiesPage />} />
              <Route path="/profile/:username" element={<ProfilePage />} />
              <Route path="/profile/:userId/followers" element={<FollowersPage />} />
              <Route path="/profile/:userId/following" element={<FollowingPage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Route>
          </Route>

          {/* Catch-all Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </AnimatePresence>
    </Router>
  );
};

export default App;
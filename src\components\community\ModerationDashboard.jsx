import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  banUser,
  unbanUser,
  removePost,
  fetchReports,
} from '../../store/slices/communitySlice';
import { Button, Input, Textarea } from '../ui';
import {
  ShieldExclamationIcon,
  UserMinusIcon,
  UserPlusIcon,
  TrashIcon,
  FlagIcon,
} from '@heroicons/react/24/outline';

const ModerationDashboard = ({ communityId, slug }) => {
  const dispatch = useDispatch();
  const { reports, bannedUsers, isModerating } = useSelector((state) => state.community);
  const [activeTab, setActiveTab] = useState('reports');
  const [banReason, setBanReason] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [removeReason, setRemoveReason] = useState('');

  useEffect(() => {
    dispatch(fetchReports(slug));
  }, [dispatch, slug]);

  const handleBan = async (userId) => {
    if (!banReason.trim()) {
      toast.error('Please provide a reason for banning');
      return;
    }
    try {
      await dispatch(banUser({ slug, userId, reason: banReason })).unwrap();
      toast.success('User banned successfully');
      setBanReason('');
      setSelectedUser(null);
    } catch (error) {
      toast.error(error.message || 'Failed to ban user');
    }
  };

  const handleUnban = async (userId) => {
    try {
      await dispatch(unbanUser({ slug, userId })).unwrap();
      toast.success('User unbanned successfully');
    } catch (error) {
      toast.error(error.message || 'Failed to unban user');
    }
  };

  const handleRemovePost = async (postId) => {
    if (!removeReason.trim()) {
      toast.error('Please provide a reason for removing the post');
      return;
    }
    try {
      await dispatch(removePost({ slug, postId, reason: removeReason })).unwrap();
      toast.success('Post removed successfully');
      setRemoveReason('');
    } catch (error) {
      toast.error(error.message || 'Failed to remove post');
    }
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm">
      <div className="border-b border-gray-200 dark:border-dark-700">
        <nav className="flex space-x-4 p-4">
          <button
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              activeTab === 'reports'
                ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('reports')}
          >
            <FlagIcon className="h-5 w-5 inline mr-2" />
            Reports
          </button>
          <button
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              activeTab === 'banned'
                ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('banned')}
          >
            <ShieldExclamationIcon className="h-5 w-5 inline mr-2" />
            Banned Users
          </button>
        </nav>
      </div>

      <div className="p-4">
        {activeTab === 'reports' ? (
          <div className="space-y-4">
            {reports.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                No reports to review
              </p>
            ) : (
              reports.map((report) => (
                <div
                  key={report._id}
                  className="border border-gray-200 dark:border-dark-700 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <img
                        src={report.reporter.avatar}
                        alt={report.reporter.username}
                        className="h-8 w-8 rounded-full"
                      />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {report.reporter.username}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(report.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                      {report.type}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {report.reason}
                  </p>
                  <div className="flex justify-end space-x-2">
                    {report.type === 'post' && (
                      <>
                        <Textarea
                          placeholder="Reason for removal"
                          value={removeReason}
                          onChange={(e) => setRemoveReason(e.target.value)}
                          rows={2}
                          className="w-full mb-2"
                        />
                        <Button
                          variant="danger"
                          onClick={() => handleRemovePost(report.contentId)}
                          loading={isModerating}
                        >
                          <TrashIcon className="h-5 w-5 inline mr-2" />
                          Remove Post
                        </Button>
                      </>
                    )}
                    <Button
                      variant="secondary"
                      onClick={() => handleBan(report.reportedUser._id)}
                      loading={isModerating}
                    >
                      <UserMinusIcon className="h-5 w-5 inline mr-2" />
                      Ban User
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {bannedUsers.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                No banned users
              </p>
            ) : (
              bannedUsers.map((ban) => (
                <div
                  key={ban._id}
                  className="border border-gray-200 dark:border-dark-700 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <img
                        src={ban.user.avatar}
                        alt={ban.user.username}
                        className="h-8 w-8 rounded-full"
                      />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {ban.user.username}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Banned on {new Date(ban.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="secondary"
                      onClick={() => handleUnban(ban.user._id)}
                      loading={isModerating}
                    >
                      <UserPlusIcon className="h-5 w-5 inline mr-2" />
                      Unban
                    </Button>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">
                    Reason: {ban.reason}
                  </p>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ModerationDashboard; 
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  GlobeAltIcon as CompassIcon,
  Cog6ToothIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

const navigation = [
  { name: 'Feed', href: '/feed', icon: HomeIcon },
  { name: 'Messages', href: '/messages', icon: ChatBubbleLeftRightIcon },
  { name: 'Communities', href: '/communities', icon: UserGroupIcon },
  { name: 'Explore', href: '/explore', icon: CompassIcon },
  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
];

const Sidebar = ({ onClose }) => {
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const { communities } = useSelector((state) => state.community);

  return (
    <div className="h-full flex flex-col bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700">
      {/* User profile */}
      <div className="p-4 border-b border-gray-200 dark:border-dark-700">
        <Link
          to={`/profile/${user?.username}`}
          className="flex items-center space-x-3"
          onClick={onClose}
        >
          {user?.avatar ? (
            <img
              src={user.avatar}
              alt={user.username}
              className="h-10 w-10 rounded-full"
            />
          ) : (
            <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
              <span className="text-primary-600 dark:text-primary-400 font-semibold">
                {user?.username?.charAt(0)?.toUpperCase() || 'U'}
              </span>
            </div>
          )}
          <div>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {user?.username || 'User'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {user?.email || '<EMAIL>'}
            </p>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.name}
              to={item.href}
              onClick={onClose}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                isActive
                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700'
              }`}
            >
              <item.icon
                className={`mr-3 h-6 w-6 ${
                  isActive
                    ? 'text-primary-600 dark:text-primary-400'
                    : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400'
                }`}
              />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* Communities */}
      <div className="flex-1 px-2 py-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Communities
          </h3>
          <Link
            to="/communities/new"
            onClick={onClose}
            className="p-1 rounded-md text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
          >
            <PlusIcon className="h-4 w-4" />
          </Link>
        </div>

        <div className="space-y-1">
          {communities?.slice(0, 5).map((community) => (
            <Link
              key={community._id}
              to={`/communities/${community._id}`}
              onClick={onClose}
              className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700"
            >
              {community.icon ? (
                <img
                  src={community.icon}
                  alt={community.name}
                  className="mr-3 h-6 w-6 rounded-full"
                />
              ) : (
                <div className="mr-3 h-6 w-6 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                  <span className="text-xs font-semibold text-primary-600 dark:text-primary-400">
                    {community.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              {community.name}
            </Link>
          ))}

          {(!communities || communities.length === 0) && (
            <p className="px-2 text-sm text-gray-500 dark:text-gray-400">
              No communities yet
            </p>
          )}

          {communities && communities.length > 5 && (
            <Link
              to="/communities"
              onClick={onClose}
              className="block px-2 py-2 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              View all communities
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

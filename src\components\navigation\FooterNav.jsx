import { NavLink } from 'react-router-dom';
import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  BellIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

const navItems = [
  { to: '/', icon: HomeIcon, label: 'Home' },
  { to: '/communities', icon: UserGroupIcon, label: 'Communities' },
  { to: '/messages', icon: ChatBubbleOvalLeftEllipsisIcon, label: 'Messages' },
  { to: '/notifications', icon: BellIcon, label: 'Notifications' },
  { to: '/profile/me', icon: UserIcon, label: 'Profile' },
];

const FooterNav = () => {
  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-background border-t border-gray-800 lg:hidden z-40">
      <nav className="flex justify-around items-center h-16">
        {navItems.map((item) => (
          <NavLink
            key={item.label}
            to={item.to}
            className={({ isActive }) =>
              `flex flex-col items-center justify-center w-full h-full transition-colors duration-200 ${
                isActive
                  ? 'text-primary-500'
                  : 'text-gray-400 hover:text-primary-500'
              }`
            }
          >
            <item.icon className="w-7 h-7" />
            <span className="sr-only">{item.label}</span>
          </NavLink>
        ))}
      </nav>
    </footer>
  );
};

export default FooterNav; 
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { createCommunity } from '../../store/slices/communitySlice';
import { uploadImage } from '../../utils/upload';
import { Button, Input, Textarea, ImageUpload } from '../../components/ui';

const CreateCommunityPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.community);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    rules: '',
    isPrivate: false,
    allowInvites: true,
    allowMemberPosts: true,
  });
  const [image, setImage] = useState(null);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Community name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Community name must be at least 3 characters';
    } else if (formData.name.length > 30) {
      newErrors.name = 'Community name must be less than 30 characters';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    if (!image) {
      newErrors.image = 'Community image is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      const imageUrl = await uploadImage(image);
      const communityData = {
        ...formData,
        image: imageUrl,
      };

      const result = await dispatch(createCommunity(communityData)).unwrap();
      toast.success('Community created successfully!');
      navigate(`/communities/${result.slug}`);
    } catch (error) {
      toast.error(error.message || 'Failed to create community');
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-2xl mx-auto p-4"
    >
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          Create New Community
        </h1>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Community Image
            </label>
            <ImageUpload
              image={image}
              setImage={setImage}
              error={errors.image}
              aspectRatio={1}
            />
          </div>

          <div>
            <Input
              label="Community Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              placeholder="Enter community name"
              maxLength={30}
            />
          </div>

          <div>
            <Textarea
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              error={errors.description}
              placeholder="Describe your community"
              rows={4}
            />
          </div>

          <div>
            <Textarea
              label="Community Rules"
              name="rules"
              value={formData.rules}
              onChange={handleChange}
              placeholder="Set community rules (optional)"
              rows={4}
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPrivate"
                name="isPrivate"
                checked={formData.isPrivate}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="isPrivate"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Make this community private
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowInvites"
                name="allowInvites"
                checked={formData.allowInvites}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="allowInvites"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Allow members to invite others
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowMemberPosts"
                name="allowMemberPosts"
                checked={formData.allowMemberPosts}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="allowMemberPosts"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Allow members to create posts
              </label>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate(-1)}
            >
              Cancel
            </Button>
            <Button type="submit" loading={isLoading}>
              Create Community
            </Button>
          </div>
        </form>
      </div>
    </motion.div>
  );
};

export default CreateCommunityPage; 
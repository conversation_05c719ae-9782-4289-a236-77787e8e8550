import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchCommunity, updateCommunity } from '../../store/slices/communitySlice';
import { Button, Input } from '../../components/ui';
import { UserIcon, ArrowUpIcon, ArrowDownIcon, TrashIcon } from '@heroicons/react/24/outline';

const roles = ['admin', 'moderator', 'member'];

const CommunityMembersPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentCommunity: community, isLoading } = useSelector((state) => state.community);
  const { user } = useSelector((state) => state.auth);
  const [search, setSearch] = useState('');
  const [filteredMembers, setFilteredMembers] = useState([]);

  useEffect(() => {
    dispatch(fetchCommunity(slug));
  }, [dispatch, slug]);

  useEffect(() => {
    if (community?.members) {
      setFilteredMembers(
        community.members.filter((m) =>
          m.username.toLowerCase().includes(search.toLowerCase()) ||
          m.realName.toLowerCase().includes(search.toLowerCase())
        )
      );
    }
  }, [community, search]);

  const isAdmin = community?.adminId === user?._id;
  const isModerator = community?.moderators?.includes(user?._id);

  // Admin/mod actions (promote/demote/ban/kick)
  const handleRoleChange = async (memberId, newRole) => {
    // This would call an API endpoint in a real app
    // For now, just a placeholder
    // await dispatch(updateCommunity({ slug, data: { ... } }))
    alert(`Change role of ${memberId} to ${newRole}`);
  };

  const handleKick = async (memberId) => {
    // This would call an API endpoint in a real app
    // For now, just a placeholder
    // await dispatch(updateCommunity({ slug, data: { ... } }))
    alert(`Kick member ${memberId}`);
  };

  if (isLoading) {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-800 rounded w-1/4 mb-6" />
          <div className="h-12 bg-gray-800 rounded mb-2" />
          <div className="h-12 bg-gray-800 rounded mb-2" />
          <div className="h-12 bg-gray-800 rounded mb-2" />
        </div>
      </div>
    );
  }

  if (!community) {
    return (
      <div className="max-w-3xl mx-auto p-4 text-center">
        <h2 className="text-2xl font-bold text-white mb-4">Community not found</h2>
        <Button onClick={() => navigate('/communities')}>Back to Communities</Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-3xl mx-auto p-4"
    >
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-white">Members of {community.name}</h1>
        <Button variant="secondary" onClick={() => navigate(`/communities/${slug}`)}>Back</Button>
      </div>
      <div className="mb-4">
        <Input
          type="text"
          placeholder="Search members..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </div>
      <div className="bg-background rounded-lg shadow-sm divide-y divide-gray-800">
        {filteredMembers.length === 0 ? (
          <div className="p-6 text-center text-gray-400">No members found.</div>
        ) : (
          filteredMembers.map((member) => (
            <div key={member.userId} className="flex items-center justify-between p-4">
              <div className="flex items-center space-x-3">
                <img src={member.avatar} alt={member.username} className="h-10 w-10 rounded-full" />
                <div>
                  <p className="text-sm font-medium text-white">{member.realName}</p>
                  <p className="text-xs text-gray-400">@{member.username}</p>
                  <p className="text-xs text-gray-500">{member.role}</p>
                </div>
              </div>
              {(isAdmin || isModerator) && user._id !== member.userId && (
                <div className="flex items-center space-x-2">
                  {isAdmin && member.role !== 'admin' && (
                    <>
                      {member.role === 'member' && (
                        <Button size="sm" onClick={() => handleRoleChange(member.userId, 'moderator')}>
                          <ArrowUpIcon className="h-4 w-4 inline" /> Promote to Mod
                        </Button>
                      )}
                      {member.role === 'moderator' && (
                        <Button size="sm" onClick={() => handleRoleChange(member.userId, 'member')}>
                          <ArrowDownIcon className="h-4 w-4 inline" /> Demote to Member
                        </Button>
                      )}
                    </>
                  )}
                  <Button size="sm" variant="danger" onClick={() => handleKick(member.userId)}>
                    <TrashIcon className="h-4 w-4 inline" /> Kick
                  </Button>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </motion.div>
  );
};

export default CommunityMembersPage; 
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Async thunks
export const fetchProfile = createAsyncThunk(
  'profile/fetchProfile',
  async (userId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/${userId}`);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch profile');
    }
  }
);

export const updateProfile = createAsyncThunk(
  'profile/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/users/profile`, profileData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update profile');
    }
  }
);

export const uploadAvatar = createAsyncThunk(
  'profile/uploadAvatar',
  async (file, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await axios.post(`${API_BASE_URL}/users/avatar`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.avatarUrl;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to upload avatar');
    }
  }
);

export const fetchFollowers = createAsyncThunk(
  'profile/fetchFollowers',
  async (userId, { rejectWithValue }) => {
    try {
      // MOCK API - REPLACE WITH YOUR ACTUAL API CALL
      const mockFollowers = [
        { _id: 'follower1', realName: 'John Doe', username: 'johndoe', avatar: '/default-avatar.svg', isFollowing: true },
        { _id: 'follower2', realName: 'Jane Smith', username: 'janesmith', avatar: '/default-avatar.svg', isFollowing: false },
      ];
      // const response = await axios.get(`${API_BASE_URL}/users/${userId}/followers`);
      // return response.data;
      return mockFollowers;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch followers');
    }
  }
);

export const fetchFollowing = createAsyncThunk(
  'profile/fetchFollowing',
  async (userId, { rejectWithValue }) => {
    try {
      // MOCK API - REPLACE WITH YOUR ACTUAL API CALL
       const mockFollowing = [
        { _id: 'following1', realName: 'Peter Jones', username: 'peterjones', avatar: '/default-avatar.svg', isFollowing: true },
      ];
      // const response = await axios.get(`${API_BASE_URL}/users/${userId}/following`);
      // return response.data;
       return mockFollowing;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch following users');
    }
  }
);

export const followUser = createAsyncThunk(
  'profile/followUser',
  async (userId, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/users/${userId}/follow`);
      return { userId, following: response.data.following };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to follow user');
    }
  }
);

export const updatePassword = createAsyncThunk(
  'profile/updatePassword',
  async (passwordData) => {
    // Placeholder
    return { message: 'Password updated' };
  }
);

export const updateNotifications = createAsyncThunk(
  'profile/updateNotifications',
  async (notificationData) => {
    // Placeholder
    return notificationData;
  }
);

export const deleteAccount = createAsyncThunk(
  'profile/deleteAccount',
  async () => {
    // Placeholder
    return { message: 'Account deleted' };
  }
);

const initialState = {
  currentProfile: null,
  viewedProfile: null,
  isLoading: false,
  isUpdating: false,
  error: null,
  followers: [],
  following: [],
  posts: [],
  stats: {
    postsCount: 0,
    followersCount: 0,
    followingCount: 0,
  },
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    setViewedProfile: (state, action) => {
      state.viewedProfile = action.payload;
    },
    updateCurrentProfile: (state, action) => {
      if (state.currentProfile) {
        state.currentProfile = { ...state.currentProfile, ...action.payload };
      }
    },
    clearViewedProfile: (state) => {
      state.viewedProfile = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch profile
      .addCase(fetchProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.viewedProfile = action.payload;
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Fetch Followers
      .addCase(fetchFollowers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFollowers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.followers = action.payload;
      })
      .addCase(fetchFollowers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
       // Fetch Following
      .addCase(fetchFollowing.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFollowing.fulfilled, (state, action) => {
        state.isLoading = false;
        state.following = action.payload;
      })
      .addCase(fetchFollowing.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      // Update profile
      .addCase(updateProfile.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.currentProfile = action.payload;
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload;
      })
      // Upload avatar
      .addCase(uploadAvatar.fulfilled, (state, action) => {
        if (state.currentProfile) {
          state.currentProfile.avatar = action.payload;
        }
      })
      // Follow user
      .addCase(followUser.fulfilled, (state, action) => {
        const { userId, following } = action.payload;
        if (state.viewedProfile && state.viewedProfile._id === userId) {
          state.viewedProfile.isFollowing = following;
          state.viewedProfile.followersCount += following ? 1 : -1;
        }
      });
  },
});

export const {
  setViewedProfile,
  updateCurrentProfile,
  clearViewedProfile,
  clearError,
} = profileSlice.actions;

export default profileSlice.reducer;

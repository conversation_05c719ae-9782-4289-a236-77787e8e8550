import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import "./DualProfilePicture.css";

/**
 * DualProfilePicture
 * Displays two profile pictures (profilePic1, profilePic2) and alternates between them every 1.5s.
 * If only one image is provided, displays it statically.
 * Uses crossfade animation for subtle, visually appealing transitions.
 */
export default function DualProfilePicture({ profilePic1, profilePic2, alt = "User profile picture", size = 64 }) {
  const [showFirst, setShowFirst] = useState(true);
  const intervalRef = useRef(null);
  const hasSecond = Boolean(profilePic2);

  useEffect(() => {
    if (!hasSecond) return;
    intervalRef.current = setInterval(() => setShowFirst((v) => !v), 1500);
    return () => clearInterval(intervalRef.current);
  }, [hasSecond]);

  const imgStyle = {
    width: size,
    height: size,
    borderRadius: "50%",
    objectFit: "cover",
    position: "absolute",
    top: 0,
    left: 0,
    transition: "opacity 0.5s cubic-bezier(.4,0,.2,1)",
    boxShadow: "0 2px 8px rgba(0,0,0,0.10)",
    background: "#f5f6fa"
  };

  return (
    <div className="dual-profile-picture" style={{ position: "relative", width: size, height: size }}>
      <img
        src={profilePic1}
        alt={alt}
        style={{ ...imgStyle, opacity: showFirst || !hasSecond ? 1 : 0, zIndex: showFirst ? 2 : 1 }}
        draggable={false}
      />
      {hasSecond && (
        <img
          src={profilePic2}
          alt={alt}
          style={{ ...imgStyle, opacity: showFirst ? 0 : 1, zIndex: showFirst ? 1 : 2 }}
          draggable={false}
        />
      )}
    </div>
  );
}

DualProfilePicture.propTypes = {
  profilePic1: PropTypes.string.isRequired,
  profilePic2: PropTypes.string,
  alt: PropTypes.string,
  size: PropTypes.number,
};

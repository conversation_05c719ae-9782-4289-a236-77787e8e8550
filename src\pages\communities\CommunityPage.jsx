import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  fetchCommunity,
  joinCommunity,
  leaveCommunity,
  updateCommunity,
} from '../../store/slices/communitySlice';
import { Button, Input, Textarea } from '../../components/ui';
import {
  UserGroupIcon,
  ShieldCheckIcon,
  UserPlusIcon,
  UserMinusIcon,
  PencilIcon,
} from '@heroicons/react/24/outline';
import CommunityFeed from '../../components/community/CommunityFeed';
import CreatePost from '../../components/community/CreatePost';
import ModerationDashboard from '../../components/community/ModerationDashboard';
import ReportContent from '../../components/community/ReportContent';

const CommunityPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentCommunity: community, isLoading } = useSelector(
    (state) => state.community
  );
  const { user } = useSelector((state) => state.auth);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    description: '',
    rules: '',
  });
  const [activeTab, setActiveTab] = useState('about');
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState(null);

  useEffect(() => {
    dispatch(fetchCommunity(slug));
  }, [dispatch, slug]);

  useEffect(() => {
    if (community) {
      setEditData({
        description: community.description,
        rules: community.rules,
      });
    }
  }, [community]);

  const isMember = community?.members?.some((m) => m.userId === user?._id);
  const isAdmin = community?.adminId === user?._id;
  const isModerator = community?.moderators?.some((m) => m.userId === user?._id);

  const handleJoin = async () => {
    try {
      await dispatch(joinCommunity(slug)).unwrap();
      toast.success('Joined community successfully!');
    } catch (error) {
      toast.error(error.message || 'Failed to join community');
    }
  };

  const handleLeave = async () => {
    try {
      await dispatch(leaveCommunity(slug)).unwrap();
      toast.success('Left community successfully!');
    } catch (error) {
      toast.error(error.message || 'Failed to leave community');
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      await dispatch(updateCommunity({ slug, data: editData })).unwrap();
      toast.success('Community updated successfully!');
      setIsEditing(false);
    } catch (error) {
      toast.error(error.message || 'Failed to update community');
    }
  };

  const handleReport = (contentId, type) => {
    setReportData({ contentId, type });
    setShowReportModal(true);
  };

  const handleCloseReport = () => {
    setShowReportModal(false);
    setReportData(null);
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto p-4">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 dark:bg-dark-700 rounded-lg mb-6" />
          <div className="h-8 bg-gray-200 dark:bg-dark-700 rounded w-1/4 mb-4" />
          <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4 mb-2" />
          <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-1/2" />
        </div>
      </div>
    );
  }

  if (!community) {
    return (
      <div className="max-w-7xl mx-auto p-4 text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Community not found
        </h2>
        <Button onClick={() => navigate('/communities')}>
          Back to Communities
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-7xl mx-auto p-4"
    >
      <div className="relative h-64 rounded-lg overflow-hidden mb-6">
        <img
          src={community.image}
          alt={community.name}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                {community.name}
              </h1>
              <div className="flex items-center space-x-4 text-white/80">
                <div className="flex items-center">
                  <UserGroupIcon className="h-5 w-5 mr-1" />
                  <span>{community.memberCount} members</span>
                </div>
                {community.isPrivate && (
                  <div className="flex items-center">
                    <ShieldCheckIcon className="h-5 w-5 mr-1" />
                    <span>Private</span>
                  </div>
                )}
              </div>
            </div>
            {!isAdmin && (
              <Button
                variant={isMember ? 'secondary' : 'primary'}
                onClick={isMember ? handleLeave : handleJoin}
                className="flex items-center space-x-2"
              >
                {isMember ? (
                  <>
                    <UserMinusIcon className="h-5 w-5" />
                    <span>Leave</span>
                  </>
                ) : (
                  <>
                    <UserPlusIcon className="h-5 w-5" />
                    <span>Join</span>
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm mb-6">
            <div className="border-b border-gray-200 dark:border-dark-700">
              <nav className="flex space-x-4 p-4">
                <button
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === 'about'
                      ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                      : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                  onClick={() => setActiveTab('about')}
                >
                  About
                </button>
                {(isAdmin || isModerator) && (
                  <button
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      activeTab === 'moderation'
                        ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                    onClick={() => setActiveTab('moderation')}
                  >
                    Moderation
                  </button>
                )}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'about' ? (
                <>
                  {isEditing ? (
                    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6 space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Description
                        </label>
                        <Textarea
                          value={editData.description}
                          onChange={(e) =>
                            setEditData((prev) => ({
                              ...prev,
                              description: e.target.value,
                            }))
                          }
                          rows={4}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Rules
                        </label>
                        <Textarea
                          value={editData.rules}
                          onChange={(e) =>
                            setEditData((prev) => ({
                              ...prev,
                              rules: e.target.value,
                            }))
                          }
                          rows={4}
                        />
                      </div>
                      <div className="flex justify-end space-x-4">
                        <Button
                          variant="secondary"
                          onClick={() => setIsEditing(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleSave}>Save Changes</Button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6 mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                          About
                        </h2>
                        {isAdmin && (
                          <Button
                            variant="secondary"
                            onClick={handleEdit}
                            className="flex items-center space-x-2"
                          >
                            <PencilIcon className="h-5 w-5" />
                            <span>Edit</span>
                          </Button>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-6">
                        {community.description}
                      </p>
                      {community.rules && (
                        <>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                            Rules
                          </h3>
                          <p className="text-gray-600 dark:text-gray-300 whitespace-pre-line">
                            {community.rules}
                          </p>
                        </>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <ModerationDashboard communityId={community._id} slug={slug} />
              )}
            </div>
          </div>

          {/* Create Post (if allowed) */}
          {isMember && community.allowMemberPosts && !isEditing && (
            <CreatePost communityId={community._id} />
          )}

          {/* Community Feed */}
          <CommunityFeed
            communityId={community._id}
            canPost={isMember && community.allowMemberPosts}
            onReport={handleReport}
          />
        </div>

        <div className="space-y-6">
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Members
            </h2>
            <div className="space-y-4">
              {community.members?.slice(0, 5).map((member) => (
                <div
                  key={member.userId}
                  className="flex items-center space-x-3"
                >
                  <img
                    src={member.avatar}
                    alt={member.username}
                    className="h-10 w-10 rounded-full"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {member.username}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {member.role}
                    </p>
                  </div>
                </div>
              ))}
              {community.memberCount > 5 && (
                <Button
                  variant="secondary"
                  className="w-full"
                  onClick={() => navigate(`/communities/${slug}/members`)}
                >
                  View All Members
                </Button>
              )}
            </div>
          </div>

          {isAdmin && (
            <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Community Settings
              </h2>
              <div className="space-y-4">
                <Button
                  variant="secondary"
                  className="w-full"
                  onClick={() => navigate(`/communities/${slug}/settings`)}
                >
                  Manage Community
                </Button>
                <Button
                  variant="secondary"
                  className="w-full"
                  onClick={() => navigate(`/communities/${slug}/moderation`)}
                >
                  Moderation Tools
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Report Modal */}
      {showReportModal && reportData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <ReportContent
            slug={slug}
            contentId={reportData.contentId}
            type={reportData.type}
            onClose={handleCloseReport}
          />
        </div>
      )}
    </motion.div>
  );
};

export default CommunityPage; 
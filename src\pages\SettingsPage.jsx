import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { updateProfile, updatePassword, updateNotifications, deleteAccount } from '../store/slices/profileSlice';
import { Button } from '../components/ui/Button';
import { Switch } from '@headlessui/react';
import { BellIcon, ShieldCheckIcon, UserCircleIcon, KeyIcon, TrashIcon } from '@heroicons/react/24/outline';

const SettingsPage = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [activeTab, setActiveTab] = useState('profile');
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    profile: {
      name: user?.name || '',
      email: user?.email || '',
      bio: user?.bio || '',
      location: user?.location || '',
      website: user?.website || ''
    },
    password: {
    currentPassword: '',
    newPassword: '',
      confirmPassword: ''
    },
    notifications: {
      mentions: user?.notifications?.mentions || false,
      comments: user?.notifications?.comments || false,
      messages: user?.notifications?.messages || false
    }
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      profile: {
        ...prev.profile,
        [name]: value
      }
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      password: {
        ...prev.password,
        [name]: value
      }
    }));
  };

  const handleNotificationChange = (type) => {
    setFormData(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [type]: !prev.notifications[type]
      }
    }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    try {
      await dispatch(updateProfile(formData.profile)).unwrap();
      setError(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    if (formData.password.newPassword !== formData.password.confirmPassword) {
      setError('New passwords do not match');
      return;
    }
    try {
      await dispatch(updatePassword(formData.password)).unwrap();
      setError(null);
      setFormData(prev => ({
        ...prev,
        password: {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      }));
    } catch (err) {
      setError(err.message);
    }
  };

  const handleNotificationsSubmit = async (e) => {
    e.preventDefault();
    try {
      await dispatch(updateNotifications(formData.notifications)).unwrap();
      setError(null);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleDeleteAccount = async () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        await dispatch(deleteAccount()).unwrap();
      } catch (err) {
        setError(err.message);
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-4">
      <div className="bg-background rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-white mb-6">Settings</h1>

        <div className="border-b border-gray-800 mb-6">
          <nav className="-mb-px flex flex-wrap space-x-8">
            <button
              onClick={() => setActiveTab('profile')}
              className={`${
                activeTab === 'profile'
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Profile
            </button>
            <button
              onClick={() => setActiveTab('password')}
              className={`${
                activeTab === 'password'
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Password
            </button>
          <button
              onClick={() => setActiveTab('notifications')}
            className={`${
                activeTab === 'notifications'
                  ? 'border-primary-500 text-primary-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Notifications
          </button>
          </nav>
      </div>

        {activeTab === 'profile' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <form onSubmit={handleProfileSubmit} className="space-y-6">
                <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300">
                  Name
                  </label>
                  <input
                    type="text"
                  name="name"
                  id="name"
                  value={formData.profile.name}
                    onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                  />
                </div>

                <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-300">
                    Email
                  </label>
                  <input
                    type="email"
                    name="email"
                  id="email"
                  value={formData.profile.email}
                    onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                  />
                </div>

                <div>
                <label htmlFor="bio" className="block text-sm font-medium text-gray-300">
                    Bio
                  </label>
                  <textarea
                    name="bio"
                  id="bio"
                  rows={3}
                  value={formData.profile.bio}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-300">
                  Location
                </label>
                <input
                  type="text"
                  name="location"
                  id="location"
                  value={formData.profile.location}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="website" className="block text-sm font-medium text-gray-300">
                  Website
                </label>
                <input
                  type="url"
                  name="website"
                  id="website"
                  value={formData.profile.website}
                    onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                />
                </div>

              <Button type="submit">Save Profile</Button>
              </form>
          </motion.div>
        )}

        {activeTab === 'password' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <form onSubmit={handlePasswordSubmit} className="space-y-6">
                <div>
                <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-300">
                    Current Password
                  </label>
                  <input
                    type="password"
                    name="currentPassword"
                    id="currentPassword"
                    value={formData.password.currentPassword}
                    onChange={handlePasswordChange}
                    className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-300">
                    New Password
                  </label>
                  <input
                    type="password"
                    name="newPassword"
                    id="newPassword"
                    value={formData.password.newPassword}
                    onChange={handlePasswordChange}
                    className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                  />
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    name="confirmPassword"
                    id="confirmPassword"
                    value={formData.password.confirmPassword}
                    onChange={handlePasswordChange}
                    className="mt-1 block w-full rounded-md border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 bg-gray-800 text-white sm:text-sm"
                  />
                </div>

              <Button type="submit">Change Password</Button>
            </form>
          </motion.div>
        )}

        {activeTab === 'notifications' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <form onSubmit={handleNotificationsSubmit} className="space-y-6">
                <Switch.Group as="div" className="flex items-center justify-between">
                  <span className="flex-grow flex flex-col">
                    <Switch.Label as="h3" className="text-sm font-medium text-white" passive>
                      Mentions
                    </Switch.Label>
                    <Switch.Description as="p" className="text-sm text-gray-400">
                      Get notified when someone mentions you
                    </Switch.Description>
                  </span>
                  <Switch
                    checked={formData.notifications.mentions}
                    onChange={() => handleNotificationChange('mentions')}
                    className={`${
                      formData.notifications.mentions ? 'bg-primary-600' : 'bg-gray-600'
                    } relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background`}
                  >
                    <span
                      className={`${
                        formData.notifications.mentions ? 'translate-x-6' : 'translate-x-1'
                      } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                    />
                  </Switch>
                </Switch.Group>
                
                <Switch.Group as="div" className="flex items-center justify-between">
                  <span className="flex-grow flex flex-col">
                    <Switch.Label as="h3" className="text-sm font-medium text-white" passive>
                      Comments
                    </Switch.Label>
                    <Switch.Description as="p" className="text-sm text-gray-400">
                      Get notified about new comments
                    </Switch.Description>
                  </span>
                  <Switch
                    checked={formData.notifications.comments}
                    onChange={() => handleNotificationChange('comments')}
                    className={`${
                      formData.notifications.comments ? 'bg-primary-600' : 'bg-gray-600'
                    } relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background`}
                  >
                    <span
                      className={`${
                        formData.notifications.comments ? 'translate-x-6' : 'translate-x-1'
                      } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                    />
                  </Switch>
                </Switch.Group>

                <Switch.Group as="div" className="flex items-center justify-between">
                  <span className="flex-grow flex flex-col">
                    <Switch.Label as="h3" className="text-sm font-medium text-white" passive>
                      Messages
                    </Switch.Label>
                    <Switch.Description as="p" className="text-sm text-gray-400">
                      Get notified about new messages
                    </Switch.Description>
                  </span>
                  <Switch
                    checked={formData.notifications.messages}
                    onChange={() => handleNotificationChange('messages')}
                    className={`${
                      formData.notifications.messages ? 'bg-primary-600' : 'bg-gray-600'
                    } relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background`}
                  >
                    <span
                      className={`${
                        formData.notifications.messages ? 'translate-x-6' : 'translate-x-1'
                      } inline-block w-4 h-4 transform bg-white rounded-full transition-transform`}
                    />
                  </Switch>
                </Switch.Group>
                
              <Button type="submit">Save Notifications</Button>
            </form>
          </motion.div>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg">
            {error}
          </div>
        )}

        {/* Delete account section */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <h3 className="text-lg font-medium text-white mb-4">Danger Zone</h3>
          <div>
            <h4 className="text-sm font-medium text-white">Delete Account</h4>
            <p className="text-sm text-gray-400">
              Once you delete your account, there is no going back. Please be certain.
            </p>
            <div className="mt-4">
              <Button variant="danger" onClick={handleDeleteAccount}>
                Delete Account
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchCommunities } from '../../store/slices/communitySlice';
import { Button, Input, Pagination } from '../../components/ui';
import { PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const CommunitiesPage = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { communities, isLoading, totalPages, currentPage } = useSelector(
    (state) => state.community
  );
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  useEffect(() => {
    dispatch(fetchCommunities({ page: currentPage, search: debouncedSearch }));
  }, [dispatch, currentPage, debouncedSearch]);

  const handlePageChange = (page) => {
    dispatch(fetchCommunities({ page, search: debouncedSearch }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-7xl mx-auto p-4"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Communities
        </h1>
        <Button
          onClick={() => navigate('/communities/create')}
          className="flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Create Community</span>
        </Button>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Input
            type="text"
            placeholder="Search communities..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div
              key={index}
              className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-4 animate-pulse"
            >
              <div className="h-32 bg-gray-200 dark:bg-dark-700 rounded-lg mb-4" />
              <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4 mb-2" />
              <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-1/2" />
            </div>
          ))}
        </div>
      ) : communities.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {communities.map((community) => (
              <motion.div
                key={community._id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.02 }}
                className="bg-white dark:bg-dark-800 rounded-lg shadow-sm overflow-hidden cursor-pointer"
                onClick={() => navigate(`/communities/${community.slug}`)}
              >
                <div className="relative h-32">
                  <img
                    src={community.image}
                    alt={community.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                </div>
                <div className="p-4">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {community.name}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    {community.description}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>{community.memberCount} members</span>
                    {community.isPrivate && (
                      <span className="bg-gray-100 dark:bg-dark-700 px-2 py-1 rounded-full">
                        Private
                      </span>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No communities found
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {search
              ? 'Try adjusting your search terms'
              : 'Be the first to create a community!'}
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default CommunitiesPage; 
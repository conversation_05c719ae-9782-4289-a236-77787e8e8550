/**
 * Centralized API Configuration
 */

import axios from 'axios';

// API Configuration Constants
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 15000, // 15 seconds timeout
  tokenKey: import.meta.env.VITE_TOKEN_STORAGE_KEY || 'accessToken',
  expiryBuffer: parseInt(import.meta.env.VITE_TOKEN_EXPIRY_BUFFER) || 300000, // 5 minutes buffer
};

/**
 * Create axios instance with base configuration
 */
const api = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

/**
 * Token Management Utilities
 */
export const tokenManager = {
  /**
   * Get stored access token
   * @returns {string|null} Access token or null if not found
   */
  getToken: () => {
    try {
      return localStorage.getItem(API_CONFIG.tokenKey);
    } catch (error) {
      console.error('Error retrieving token:', error);
      return null;
    }
  },

  /**
   * Store access token
   * @param {string} token - JWT access token
   */
  setToken: (token) => {
    try {
      if (token) {
        localStorage.setItem(API_CONFIG.tokenKey, token);
      }
    } catch (error) {
      console.error('Error storing token:', error);
    }
  },

  /**
   * Remove stored access token
   */
  removeToken: () => {
    try {
      localStorage.removeItem(API_CONFIG.tokenKey);
    } catch (error) {
      console.error('Error removing token:', error);
    }
  },

  /**
   * Check if token exists
   * @returns {boolean} True if token exists
   */
  hasToken: () => {
    return !!tokenManager.getToken();
  },
};

/**
 * Request Interceptor
 * Automatically attaches JWT token to requests
 */
api.interceptors.request.use(
  (config) => {
    const token = tokenManager.getToken();
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Log request in development
    if (import.meta.env.VITE_NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

/**
 * Response Interceptor
 * Handles authentication errors and token refresh
 */
api.interceptors.response.use(
  (response) => {
    // Log successful response in development
    if (import.meta.env.VITE_NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.status);
    }

    return response;
  },
  (error) => {
    const { response, config } = error;

    // Log error in development
    if (import.meta.env.VITE_NODE_ENV === 'development') {
      console.error(`❌ API Error: ${config?.method?.toUpperCase()} ${config?.url}`, response?.status, response?.data);
    }

    // Handle authentication errors
    if (response?.status === 401) {
      // Token is invalid or expired
      tokenManager.removeToken();
      
      // Dispatch logout action if store is available
      if (window.__REDUX_STORE__) {
        window.__REDUX_STORE__.dispatch({ type: 'auth/logout/fulfilled' });
      }
      
      // Redirect to login page if not already there
      if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
        window.location.href = '/login';
      }
    }

    // Handle network errors
    if (!response) {
      error.message = 'Network error. Please check your connection and try again.';
    }

    // Handle server errors
    if (response?.status >= 500) {
      error.message = 'Server error. Please try again later.';
    }

    return Promise.reject(error);
  }
);

/**
 * API Helper Functions
 */
export const apiHelpers = {
  /**
   * Handle API errors consistently
   * @param {Error} error - Axios error object
   * @returns {string} User-friendly error message
   */
  handleError: (error) => {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error.message) {
      return error.message;
    }
    
    return 'An unexpected error occurred. Please try again.';
  },

  /**
   * Create standardized error response
   * @param {string} message - Error message
   * @param {number} status - HTTP status code
   * @returns {Object} Standardized error object
   */
  createError: (message, status = 500) => ({
    message,
    status,
    timestamp: new Date().toISOString(),
  }),
};

/**
 * API Endpoints Configuration
 * Centralized endpoint definitions for consistency
 */
export const API_ENDPOINTS = {
  // Authentication endpoints (matching your backend structure)
  AUTH: {
    REGISTER: '/user/register',
    LOGIN: '/user/login',
    PROFILE: '/user/profile',
  },
  
  // Future endpoints (when backend team implements them)
  POSTS: {
    BASE: '/posts',
    LIKE: (id) => `/posts/${id}/like`,
    COMMENTS: (id) => `/posts/${id}/comments`,
  },
  
  FEED: {
    BASE: '/feed',
  },
  
  USERS: {
    BASE: '/users',
    FOLLOW: (id) => `/users/${id}/follow`,
  },
};

// Export configured axios instance as default
export default api;

import { useState, useEffect } from 'react';
import { Input } from '../../ui';
import { LinkIcon } from '@heroicons/react/24/outline';

const LinkPost = ({ onChange, value }) => {
  const [url, setUrl] = useState('');
  const [preview, setPreview] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchPreview = async () => {
      if (!url) {
        setPreview(null);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/link-preview?url=${encodeURIComponent(url)}`);
        const data = await response.json();
        setPreview(data);
        onChange({ url, ...data });
      } catch (error) {
        console.error('Error fetching link preview:', error);
        setPreview(null);
      } finally {
        setIsLoading(false);
      }
    };

    const timeoutId = setTimeout(fetchPreview, 1000);
    return () => clearTimeout(timeoutId);
  }, [url]);

  const handleUrlChange = (e) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    if (!newUrl) {
      onChange('');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <LinkIcon className="h-5 w-5 text-gray-400" />
        <Input
          type="url"
          value={url}
          onChange={handleUrlChange}
          placeholder="Paste a link here"
          className="flex-1"
        />
      </div>

      {isLoading && (
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 dark:bg-dark-700 rounded-lg"></div>
        </div>
      )}

      {preview && !isLoading && (
        <div className="border border-gray-200 dark:border-dark-700 rounded-lg overflow-hidden">
          {preview.image && (
            <img
              src={preview.image}
              alt={preview.title}
              className="w-full h-48 object-cover"
            />
          )}
          <div className="p-4">
            <h3 className="font-medium text-gray-900 dark:text-white">
              {preview.title}
            </h3>
            {preview.description && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {preview.description}
              </p>
            )}
            <p className="mt-2 text-xs text-gray-400 dark:text-gray-500">
              {preview.siteName || new URL(url).hostname}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default LinkPost; 
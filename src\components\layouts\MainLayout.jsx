import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { Toaster } from 'react-hot-toast';
import Sidebar from '../navigation/Sidebar';
import TopBar from '../navigation/TopBar';
import { toggleSidebar } from '../../store/slices/uiSlice';
import FooterNav from '../navigation/FooterNav';

const MainLayout = ({ children }) => {
  const dispatch = useDispatch();
  const { sidebarCollapsed } = useSelector((state) => state.ui);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleSidebarToggle = () => {
    dispatch(toggleSidebar());
  };

  return (
    <>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />
      <div className="flex h-screen bg-background">
        {/* Sidebar */}
        <AnimatePresence mode="wait">
          {(!isMobile || !sidebarCollapsed) && (
            <motion.div
              initial={{ x: isMobile ? -280 : 0, opacity: isMobile ? 0 : 1 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: isMobile ? -280 : 0, opacity: isMobile ? 0 : 1 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className={`
                ${isMobile ? 'fixed inset-y-0 left-0 z-50' : 'relative'}
                ${sidebarCollapsed && !isMobile ? 'w-16' : 'w-64'}
                bg-background border-r border-gray-800
                transition-all duration-300 ease-in-out
              `}
            >
              <Sidebar 
                collapsed={sidebarCollapsed && !isMobile} 
                isMobile={isMobile}
                onToggle={handleSidebarToggle}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mobile overlay */}
        {isMobile && !sidebarCollapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={handleSidebarToggle}
          />
        )}

        {/* Main content area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top bar */}
          <TopBar onSidebarToggle={handleSidebarToggle} />

          {/* Page content */}
          <main className="flex-1 overflow-y-auto bg-background pb-16 lg:pb-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="h-full"
            >
              {children}
            </motion.div>
          </main>
        </div>
        <FooterNav />
      </div>
    </>
  );
};

export default MainLayout;

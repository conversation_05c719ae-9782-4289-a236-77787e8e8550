import { useState } from 'react';
import { Textarea } from '../../ui';
import { DocumentTextIcon } from '@heroicons/react/24/outline';

const TextPost = ({ onChange, value }) => {
  const [charCount, setCharCount] = useState(0);
  const MAX_CHARS = 40000;

  const handleChange = (e) => {
    const text = e.target.value;
    if (text.length <= MAX_CHARS) {
      setCharCount(text.length);
      onChange(text);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
        <DocumentTextIcon className="h-5 w-5" />
        <span className="text-sm">
          {charCount}/{MAX_CHARS} characters
        </span>
      </div>
      <Textarea
        value={value}
        onChange={handleChange}
        placeholder="What's on your mind?"
        className="min-h-[200px]"
      />
    </div>
  );
};

export default TextPost; 
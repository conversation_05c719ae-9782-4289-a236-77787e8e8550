/**
 * Authentication Initialization Hook
 * 
 * This hook handles:
 * - Checking for existing tokens on app startup
 * - Validating tokens with the backend
 * - Setting up initial authentication state
 * - Handling token expiration
 */

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentUser, clearAuth } from '../store/slices/authSlice';
import { tokenManager } from '../utils/api';

/**
 * Custom hook to initialize authentication state
 * Should be called once in the main App component
 */
export const useAuthInit = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading, user } = useSelector((state) => state.auth);

  useEffect(() => {
    const initializeAuth = async () => {
      const token = tokenManager.getToken();
      
      if (token && !user) {
        // Token exists but no user data - validate token with backend
        try {
          await dispatch(getCurrentUser()).unwrap();
        } catch (error) {
          // Token is invalid or expired
          console.warn('Token validation failed:', error);
          dispatch(clearAuth());
        }
      } else if (!token && isAuthenticated) {
        // No token but auth state says authenticated - clear auth state
        dispatch(clearAuth());
      }
    };

    initializeAuth();
  }, [dispatch, user, isAuthenticated]);

  return {
    isAuthenticated,
    isLoading,
    user,
  };
};

export default useAuthInit;

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { fetchProfile } from '../store/slices/profileSlice';
import { fetchUserPosts } from '../store/slices/postSlice';
import { Button } from '../components/ui/Button';
import { UserIcon, CalendarIcon, LinkIcon, MapPinIcon } from '@heroicons/react/24/outline';

const ProfilePage = () => {
  const { userId } = useParams();
  const dispatch = useDispatch();
  const { user: currentUser } = useSelector((state) => state.auth);
  const { profile, isLoading: profileLoading } = useSelector((state) => state.profile);
  const { posts, isLoading: postsLoading } = useSelector((state) => state.post);
  const [activeTab, setActiveTab] = useState('posts');
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    setIsOwnProfile(!userId || userId === currentUser?._id);
    if (userId || currentUser?._id) {
      dispatch(fetchProfile(userId || currentUser?._id));
      dispatch(fetchUserPosts(userId || currentUser?._id));
    }
  }, [userId, currentUser, dispatch]);

  const tabs = [
    { id: 'posts', label: 'Posts', count: profile?.stats?.posts || 0 },
    { id: 'media', label: 'Media', count: profile?.stats?.media || 0 },
    { id: 'likes', label: 'Likes', count: profile?.stats?.likes || 0 },
    { id: 'about', label: 'About' },
  ];

  const handleFollow = async () => {
    // Implement follow/unfollow functionality
    try {
      // await dispatch(followUser(profile._id));
      // Update profile stats
    } catch (error) {
      console.error('Failed to follow/unfollow:', error);
    }
  };

  if (profileLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-32 bg-gray-800 rounded-lg mb-6"></div>
        <div className="space-y-4">
          <div className="h-8 bg-gray-800 rounded w-1/4"></div>
          <div className="h-32 bg-gray-800 rounded"></div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-white mb-4">
          Profile Not Found
        </h2>
        <p className="text-gray-400">
          The profile you're looking for doesn't exist or has been removed.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-4xl mx-auto"
    >
      <div className="bg-background rounded-lg shadow-sm overflow-hidden">
        <div className="relative h-32 bg-gradient-to-r from-primary-500 to-primary-600">
          {profile.banner && (
            <img
              src={profile.banner}
              alt={profile.name}
              className="w-full h-full object-cover"
            />
          )}
        </div>

        <div className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <img
                src={profile.avatar}
                alt={profile.name}
                className="h-16 w-16 rounded-lg"
              />
              <div>
                <h1 className="text-2xl font-bold text-white">
                  {profile.name} <span className="text-gray-400">@{profile.username}</span>
                </h1>
              </div>
            </div>

            {!isOwnProfile && (
              <Button
                variant={profile.isFollowing ? 'secondary' : 'primary'}
                onClick={handleFollow}
              >
                {profile.isFollowing ? 'Unfollow' : 'Follow'}
              </Button>
            )}
          </div>

          <div className="mt-6">
            <div className="border-b border-gray-800">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-400'
                        : 'border-transparent text-gray-500 hover:text-gray-300'
                    } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                  >
                    {tab.label}
                    {tab.count !== undefined && (
                      <span className="ml-2 text-gray-400">
                        {tab.count}
                      </span>
                    )}
                  </button>
                ))}
              </nav>
            </div>

            <div className="mt-6">
              {activeTab === 'posts' && (
                <div className="space-y-6">
                  {postsLoading ? (
                    <div className="animate-pulse space-y-4">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="h-32 bg-gray-800 rounded"></div>
                      ))}
                    </div>
                  ) : posts && posts.length > 0 ? (
                    posts.map((post) => (
                      <div
                        key={post._id}
                        className="bg-gray-900 rounded-lg shadow-sm p-4"
                      >
                        <p className="text-white">{post.content}</p>
                        {post.image && (
                          <img
                            src={post.image}
                            alt="Post"
                            className="mt-4 rounded-lg max-h-96 w-full object-cover"
                          />
                        )}
                        <div className="mt-4 flex items-center space-x-4 text-sm text-gray-400">
                          <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                          <span>{post.likes} likes</span>
                          <span>{post.comments} comments</span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-gray-400">
                      No posts yet
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'about' && (
                <div className="space-y-6">
                  {profile.bio && (
                    <div>
                      <h2 className="text-lg font-medium text-white mb-2">
                        Bio
                      </h2>
                      <p className="text-gray-300">{profile.bio}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {profile.location && (
                      <div className="flex items-center space-x-2">
                        <MapPinIcon className="h-5 w-5 text-gray-400" />
                        <span className="text-gray-300">
                          {profile.location}
                        </span>
                      </div>
                    )}

                    {profile.website && (
                      <div className="flex items-center space-x-2">
                        <LinkIcon className="h-5 w-5 text-gray-400" />
                        <a
                          href={profile.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-400 hover:underline"
                        >
                          {profile.website}
                        </a>
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <CalendarIcon className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-300">
                        Joined {new Date(profile.joinedDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-lg font-medium text-white mb-2">
                      Stats
                    </h2>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">
                          {profile.stats?.posts || 0}
                        </div>
                        <div className="text-sm text-gray-400">Posts</div>
                      </div>
                      <Link to={`/profile/${profile._id}/followers`} className="text-center hover:bg-gray-800 rounded-lg p-2">
                        <div className="text-2xl font-bold text-white">
                          {profile.stats?.followers || 0}
                        </div>
                        <div className="text-sm text-gray-400">Followers</div>
                      </Link>
                      <Link to={`/profile/${profile._id}/following`} className="text-center hover:bg-gray-800 rounded-lg p-2">
                        <div className="text-2xl font-bold text-white">
                          {profile.stats?.following || 0}
                        </div>
                        <div className="text-sm text-gray-400">Following</div>
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProfilePage;

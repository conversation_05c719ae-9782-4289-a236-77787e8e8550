import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { fetchTrendingTopics } from '../store/slices/exploreSlice';
import { fetchCommunities } from '../store/slices/communitySlice';

const AnimatedCard = ({ children, delay = 0 }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay }}
    className="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-sm p-6"
  >
    {children}
  </motion.div>
);

const LandingPage = () => {
  const dispatch = useDispatch();
  const { trendingTopics = [], isLoading: exploreLoading, error: exploreError } = useSelector((state) => state.explore);
  const { communities = [], isLoading: communitiesLoading, error: communitiesError } = useSelector((state) => state.communities);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      await Promise.all([
        dispatch(fetchTrendingTopics()),
        dispatch(fetchCommunities()),
      ]);
    } catch (err) {
      console.error('Error loading landing page data:', err);
      setError('Failed to load data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  useEffect(() => {
    let isMounted = true;
    loadData();
    return () => { isMounted = false; };
  }, [loadData]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 max-w-md">
          <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-4">
            {error}
          </h3>
          <button
            onClick={loadData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-gray-50 dark:bg-gray-900 sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-4xl tracking-tight font-extrabold text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
                >
                  <span className="block">Welcome to</span>
                  <span className="block text-blue-600 dark:text-blue-400">NeTuArk</span>
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="mt-3 text-base text-gray-500 dark:text-gray-400 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"
                >
                  Join our vibrant community to share ideas, connect with others, and explore trending topics.
                </motion.p>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"
                >
                  <div className="rounded-md shadow">
                    <Link
                      to="/register"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10"
                    >
                      Get started
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      to="/explore"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 md:py-4 md:text-lg md:px-10"
                    >
                      Explore
                    </Link>
                  </div>
                </motion.div>
              </div>
            </main>
          </div>
        </div>
      </div>

      {/* Trending Topics Section */}
      <div className="py-12 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
              Trending Topics
            </h2>
            <p className="mt-3 max-w-2xl mx-auto text-xl text-gray-500 dark:text-gray-400 sm:mt-4">
              Discover what's happening right now
            </p>
          </div>

          {!Array.isArray(trendingTopics) || trendingTopics.length === 0 ? (
            <div className="mt-12 text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                No trending topics found. Check back later!
              </p>
            </div>
          ) : (
            <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {Array.isArray(trendingTopics) && trendingTopics.slice(0, 6).map((topic) => (
                <AnimatedCard key={topic.id}>
                  <div className="flex items-center space-x-4">
                    <img
                      src={topic.image}
                      alt={`Thumbnail for ${topic.title}`}
                      className="w-12 h-12 rounded-lg object-cover"
                      loading="lazy"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {topic.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {topic.posts} posts
                      </p>
                    </div>
                  </div>
                </AnimatedCard>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Popular Communities Section */}
      <div className="py-12 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
              Popular Communities
            </h2>
            <p className="mt-3 max-w-2xl mx-auto text-xl text-gray-500 dark:text-gray-400 sm:mt-4">
              Join communities that match your interests
            </p>
          </div>

          {!Array.isArray(communities) || communities.length === 0 ? (
            <div className="mt-12 text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                No communities found. Be the first to create one!
              </p>
            </div>
          ) : (
            <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
              {Array.isArray(communities) && communities.slice(0, 6).map((community) => (
                <AnimatedCard key={community.id}>
                  <div className="flex items-center space-x-4">
                    <img
                      src={community.image}
                      alt={`Thumbnail for ${community.name}`}
                      className="w-12 h-12 rounded-lg object-cover"
                      loading="lazy"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {community.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {community.members} members
                      </p>
                    </div>
                  </div>
                  <p className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                    {community.description}
                  </p>
                </AnimatedCard>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Features Section */}
      <div className="py-12 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
              Features
            </h2>
            <p className="mt-3 max-w-2xl mx-auto text-xl text-gray-500 dark:text-gray-400 sm:mt-4">
              Everything you need to connect and share
            </p>
          </div>

          <div className="mt-12 grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: 'Create Communities',
                description: 'Start your own community and invite others to join.',
                icon: '👥',
              },
              {
                title: 'Share Posts',
                description: 'Share your thoughts, images, and links with the community.',
                icon: '📝',
              },
              {
                title: 'Real-time Chat',
                description: 'Connect with others through our real-time chat system.',
                icon: '💬',
              },
              {
                title: 'Notifications',
                description: 'Stay updated with real-time notifications.',
                icon: '🔔',
              },
              {
                title: 'Content Filtering',
                description: 'Keep your community safe with our content filtering system.',
                icon: '🛡️',
              },
              {
                title: 'Dark Mode',
                description: 'Enjoy a comfortable viewing experience with dark mode support.',
                icon: '🌙',
              },
            ].map((feature, index) => (
              <AnimatedCard key={feature.title} delay={index * 0.1}>
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {feature.title}
                </h3>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  {feature.description}
                </p>
              </AnimatedCard>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 dark:bg-blue-700">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
            <span className="block">Ready to get started?</span>
            <span className="block text-blue-200">Join our community today.</span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-md shadow">
              <Link
                to="/register"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50"
              >
                Get started
              </Link>
            </div>
            <div className="ml-3 inline-flex rounded-md shadow">
              <Link
                to="/explore"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600"
              >
                Learn more
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
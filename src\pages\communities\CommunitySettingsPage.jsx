import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  fetchCommunity,
  updateCommunity,
} from '../../store/slices/communitySlice';
import { uploadImage } from '../../utils/upload';
import { Button, Input, Textarea, ImageUpload } from '../../components/ui';
import {
  ShieldCheckIcon,
  UserPlusIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import TransferOwnership from '../../components/community/TransferOwnership';
import DeleteCommunity from '../../components/community/DeleteCommunity';

const CommunitySettingsPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentCommunity: community, isLoading } = useSelector(
    (state) => state.community
  );
  const { user } = useSelector((state) => state.auth);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    rules: '',
    isPrivate: false,
    allowInvites: true,
    allowMemberPosts: true,
  });
  const [image, setImage] = useState(null);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  useEffect(() => {
    dispatch(fetchCommunity(slug));
  }, [dispatch, slug]);

  useEffect(() => {
    if (community) {
      setFormData({
        name: community.name,
        description: community.description,
        rules: community.rules,
        isPrivate: community.isPrivate,
        allowInvites: community.allowInvites,
        allowMemberPosts: community.allowMemberPosts,
      });
      setImage(community.image);
    }
  }, [community]);

  const isAdmin = community?.adminId === user?._id;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) {
      newErrors.name = 'Community name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Community name must be at least 3 characters';
    } else if (formData.name.length > 30) {
      newErrors.name = 'Community name must be less than 30 characters';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      let imageUrl = image;
      if (image && typeof image === 'object') {
        imageUrl = await uploadImage(image);
      }

      const communityData = {
        ...formData,
        image: imageUrl,
      };

      await dispatch(updateCommunity({ slug, data: communityData })).unwrap();
      toast.success('Community settings updated successfully!');
      navigate(`/communities/${slug}`);
    } catch (error) {
      toast.error(error.message || 'Failed to update community settings');
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto p-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-dark-700 rounded w-1/4 mb-6" />
          <div className="h-64 bg-gray-200 dark:bg-dark-700 rounded-lg mb-6" />
          <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-3/4 mb-2" />
          <div className="h-4 bg-gray-200 dark:bg-dark-700 rounded w-1/2" />
        </div>
      </div>
    );
  }

  if (!community || !isAdmin) {
    return (
      <div className="max-w-7xl mx-auto p-4 text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Access Denied
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          You don't have permission to access this page.
        </p>
        <Button onClick={() => navigate(`/communities/${slug}`)}>
          Back to Community
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-7xl mx-auto p-4"
    >
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Community Settings
        </h1>
        <Button
          variant="secondary"
          onClick={() => navigate(`/communities/${slug}`)}
        >
          Back to Community
        </Button>
      </div>

      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Community Image
            </label>
            <ImageUpload
              image={image}
              setImage={setImage}
              error={errors.image}
              aspectRatio={1}
            />
          </div>

          <div>
            <Input
              label="Community Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              error={errors.name}
              placeholder="Enter community name"
              maxLength={30}
            />
          </div>

          <div>
            <Textarea
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              error={errors.description}
              placeholder="Describe your community"
              rows={4}
            />
          </div>

          <div>
            <Textarea
              label="Community Rules"
              name="rules"
              value={formData.rules}
              onChange={handleChange}
              placeholder="Set community rules (optional)"
              rows={4}
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPrivate"
                name="isPrivate"
                checked={formData.isPrivate}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="isPrivate"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Make this community private
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowInvites"
                name="allowInvites"
                checked={formData.allowInvites}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="allowInvites"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Allow members to invite others
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="allowMemberPosts"
                name="allowMemberPosts"
                checked={formData.allowMemberPosts}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="allowMemberPosts"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Allow members to create posts
              </label>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-dark-700 pt-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Danger Zone
            </h2>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <UserGroupIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">
                      Transfer Ownership
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Transfer ownership of this community to another member
                    </p>
                  </div>
                </div>
                <Button
                  variant="secondary"
                  onClick={() => setShowTransferModal(true)}
                >
                  Transfer
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="flex items-center space-x-3">
                  <TrashIcon className="h-5 w-5 text-red-400" />
                  <div>
                    <h3 className="font-medium text-red-800 dark:text-red-200">
                      Delete Community
                    </h3>
                    <p className="text-sm text-red-700 dark:text-red-300">
                      Permanently delete this community and all its content
                    </p>
                  </div>
                </div>
                <Button
                  variant="danger"
                  onClick={() => setShowDeleteModal(true)}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Button type="submit" loading={isLoading}>
              Save Changes
            </Button>
          </div>
        </form>
      </div>

      {showTransferModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <TransferOwnership
            community={community}
            onClose={() => setShowTransferModal(false)}
          />
        </div>
      )}

      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <DeleteCommunity
            community={community}
            onClose={() => setShowDeleteModal(false)}
          />
        </div>
      )}
    </motion.div>
  );
};

export default CommunitySettingsPage; 
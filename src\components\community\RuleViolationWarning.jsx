import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { getViolationMessage, getViolationAction } from '../../utils/contentFilter';

const RuleViolationWarning = ({ violation, onDismiss, onAppeal }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getActionColor = (action) => {
    switch (action) {
      case 'warn':
        return 'yellow';
      case 'remove':
        return 'orange';
      case 'ban':
        return 'red';
      default:
        return 'yellow';
    }
  };

  const actionColor = getActionColor(getViolationAction(violation));

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`bg-${actionColor}-50 dark:bg-${actionColor}-900/20 border border-${actionColor}-200 dark:border-${actionColor}-800 rounded-lg p-4`}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <ExclamationTriangleIcon
            className={`h-5 w-5 text-${actionColor}-400`}
          />
        </div>
        <div className="ml-3 flex-1">
          <h3
            className={`text-sm font-medium text-${actionColor}-800 dark:text-${actionColor}-200`}
          >
            Content Warning
          </h3>
          <div className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            <p>{getViolationMessage(violation)}</p>
            {violation.details && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-1 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
              >
                {isExpanded ? 'Hide details' : 'Show details'}
              </button>
            )}
          </div>
          <AnimatePresence>
            {isExpanded && violation.details && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-2"
              >
                <div className="bg-white dark:bg-dark-800 rounded-md p-3 text-sm">
                  <pre className="whitespace-pre-wrap font-mono text-xs">
                    {JSON.stringify(violation.details, null, 2)}
                  </pre>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          <div className="mt-4 flex space-x-3">
            {onAppeal && (
              <button
                type="button"
                onClick={onAppeal}
                className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-${actionColor}-700 dark:text-${actionColor}-300 bg-${actionColor}-100 dark:bg-${actionColor}-900/50 hover:bg-${actionColor}-200 dark:hover:bg-${actionColor}-800/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${actionColor}-500`}
              >
                Appeal
              </button>
            )}
            <button
              type="button"
              onClick={onDismiss}
              className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-${actionColor}-700 dark:text-${actionColor}-300 bg-${actionColor}-100 dark:bg-${actionColor}-900/50 hover:bg-${actionColor}-200 dark:hover:bg-${actionColor}-800/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${actionColor}-500`}
            >
              Dismiss
            </button>
          </div>
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            type="button"
            onClick={onDismiss}
            className={`inline-flex text-${actionColor}-400 hover:text-${actionColor}-500 dark:hover:text-${actionColor}-300`}
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default RuleViolationWarning; 
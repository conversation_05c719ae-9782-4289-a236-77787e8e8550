# 🚀 Netlify Deployment Guide

## ✅ Pre-Deployment Checklist

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Verify build integrity**:
   ```bash
   npm run check-build
   ```

3. **Check current build files**:
   - Current JS file: `index-8ryyju1y.js`
   - Current CSS file: `index-GahzlhkL.css`

## 🔧 Deployment Steps

### Option 1: Automatic Deployment (Recommended)
1. Push your changes to GitHub
2. Netlify will automatically build and deploy

### Option 2: Manual Deployment
1. Build locally: `npm run build`
2. Deploy: `netlify deploy --prod --dir=dist`

## 🐛 Troubleshooting 404 Errors

If you see errors like:
```
GET https://netuark.netlify.app/assets/index-CxLpq8Oe.css net::ERR_ABORTED 404 (Not Found)
GET https://netuark.netlify.app/assets/index-BrRGVAwx.js net::ERR_ABORTED 404 (Not Found)
```

**This means you're deploying an old build!**

### Solutions:

1. **Clear build cache and rebuild**:
   ```bash
   rm -rf dist
   npm run build
   ```

2. **Force new deployment**:
   ```bash
   netlify deploy --prod --dir=dist
   ```

3. **Check Netlify build logs**:
   - Go to your Netlify dashboard
   - Check the build logs for errors
   - Ensure the build command is `npm run build`
   - Ensure the publish directory is `dist`

4. **Clear browser cache**:
   - Hard refresh (Ctrl+F5 or Cmd+Shift+R)
   - Or open in incognito/private mode

## 📋 Current Configuration

### netlify.toml
```toml
[build]
  command = "npm run build"
  publish = "dist"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.mjs"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
```

### _redirects
```
# API routes
/api/*  /.netlify/functions/:splat  200

# SPA fallback for all other routes (but not for existing files)
/*    /index.html   200
```

## 🔍 Verification Steps

After deployment:

1. **Check asset URLs directly**:
   - https://netuark.netlify.app/assets/index-8ryyju1y.js
   - https://netuark.netlify.app/assets/index-GahzlhkL.css

2. **Test SPA routing**:
   - Navigate to different routes
   - Refresh the page on a route like `/feed`

3. **Check browser console**:
   - Should have no 404 errors
   - Should have no MIME type errors

## 🚨 Important Notes

- **Always rebuild before deploying** if you made changes
- **File names change with each build** due to content hashing
- **The `force = false` setting** ensures existing files aren't redirected
- **Clear caches** if you see old file references

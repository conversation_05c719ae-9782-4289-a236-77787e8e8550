# API Integration Documentation

## Overview

This document explains how the frontend is integrated with the backend API, including authentication flow, error handling, and best practices.

## Architecture

### Centralized API Configuration

The frontend uses a centralized API configuration located in `src/utils/api.js` that provides:

- **Axios Instance**: Pre-configured with base URL, timeout, and headers
- **Request Interceptors**: Automatically attach JWT tokens to requests
- **Response Interceptors**: Handle authentication errors and token refresh
- **Error Handling**: Consistent error message formatting
- **Token Management**: Secure token storage and retrieval

### Environment Configuration

API configuration is managed through environment variables:

```env
VITE_API_BASE_URL=http://localhost:3000/api  # Backend API base URL
VITE_TOKEN_STORAGE_KEY=accessToken           # Token storage key
VITE_TOKEN_EXPIRY_BUFFER=300000             # Token expiry buffer (5 minutes)
```

## Authentication Flow

### 1. User Registration
- **Endpoint**: `POST /api/user/register`
- **Payload**: `{ username, email, password, confirmPassword }`
- **Response**: `{ message, user: { id, username, email, createdAt } }`
- **Flow**: User registers → Success message → Redirect to login

### 2. User Login
- **Endpoint**: `POST /api/user/login`
- **Payload**: `{ email, password }`
- **Response**: `{ message, accessToken, user: { id, username, email } }`
- **Flow**: User logs in → Token stored → User authenticated → Redirect to feed

### 3. Profile Access
- **Endpoint**: `GET /api/user/profile`
- **Headers**: `Authorization: Bearer {accessToken}`
- **Response**: `{ id, username, email, createdAt }`
- **Flow**: Authenticated request → Profile data returned

### 4. Token Management
- **Storage**: localStorage with key `accessToken`
- **Attachment**: Automatic via request interceptor
- **Expiration**: Handled by response interceptor (401 → logout)
- **Cleanup**: Removed on logout or authentication errors

## Error Handling

### Network Errors
- **Detection**: No response object
- **Message**: "Network error. Please check your connection and try again."
- **Action**: Display error to user

### Authentication Errors (401)
- **Detection**: Response status 401
- **Action**: 
  1. Remove token from storage
  2. Clear authentication state
  3. Redirect to login page

### Server Errors (5xx)
- **Detection**: Response status >= 500
- **Message**: "Server error. Please try again later."
- **Action**: Display error to user

### Validation Errors (400)
- **Detection**: Response status 400
- **Message**: From `response.data.message`
- **Action**: Display specific validation error

## Redux Integration

### Auth Slice Structure
```javascript
{
  user: null,              // User object from backend
  token: "jwt_token",      // JWT access token
  isAuthenticated: false,  // Authentication status
  isLoading: false,        // Loading state
  error: null,             // Error message
  message: null            // Success message
}
```

### Available Actions
- `loginUser(credentials)` - Login with email/password
- `registerUser(userData)` - Register new user
- `getCurrentUser()` - Get current user profile
- `logoutUser()` - Logout and clear data
- `clearError()` - Clear error message
- `clearMessage()` - Clear success message
- `setCredentials({ user, token })` - Set auth data manually
- `clearAuth()` - Clear all auth data

## Usage Examples

### Making Authenticated Requests
```javascript
import api from '../utils/api';

// Token is automatically attached
const response = await api.get('/user/profile');
const userData = response.data;
```

### Handling Errors
```javascript
import { apiHelpers } from '../utils/api';

try {
  const response = await api.post('/user/login', credentials);
  return response.data;
} catch (error) {
  const errorMessage = apiHelpers.handleError(error);
  // Display errorMessage to user
}
```

### Using Auth Hook
```javascript
import { useAuthInit } from '../hooks/useAuthInit';

const MyComponent = () => {
  const { isAuthenticated, isLoading, user } = useAuthInit();
  
  if (isLoading) return <LoadingSpinner />;
  if (!isAuthenticated) return <LoginPrompt />;
  
  return <AuthenticatedContent user={user} />;
};
```

## Security Considerations

### Token Storage
- **Method**: localStorage (consider httpOnly cookies for production)
- **Key**: Configurable via environment variable
- **Cleanup**: Automatic on logout/errors

### Request Security
- **HTTPS**: Use HTTPS in production
- **CORS**: Backend configured for specific origins
- **Headers**: Proper Content-Type and Authorization headers

### Error Handling
- **No Sensitive Data**: Error messages don't expose sensitive information
- **Automatic Cleanup**: Tokens cleared on authentication failures
- **Redirect**: Automatic redirect to login on unauthorized access

## Development vs Production

### Development
- **API URL**: `http://localhost:3000/api`
- **Logging**: Detailed request/response logging
- **Error Details**: Full error information displayed

### Production
- **API URL**: Your deployed backend URL
- **Logging**: Minimal logging
- **Error Handling**: User-friendly error messages only

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure backend CORS is configured for frontend origin
   - Check that credentials are enabled if needed

2. **Token Not Attached**
   - Verify token exists in localStorage
   - Check request interceptor is working
   - Ensure API instance is being used (not raw axios)

3. **Authentication Loops**
   - Check token expiration handling
   - Verify logout action clears all state
   - Ensure redirect logic is correct

4. **Environment Variables Not Loading**
   - Verify .env file exists and is not in .gitignore
   - Ensure variables start with VITE_
   - Restart development server after changes

## Future Enhancements

- **Token Refresh**: Implement automatic token refresh
- **Offline Support**: Add offline request queuing
- **Request Caching**: Implement response caching
- **Rate Limiting**: Add client-side rate limiting
- **Request Retry**: Implement automatic retry logic

// Content filtering rules
const CONTENT_RULES = {
  profanity: {
    enabled: true,
    words: [
      // Add profanity list here
    ],
    action: 'warn',
  },
  spam: {
    enabled: true,
    maxLinks: 3,
    maxMentions: 5,
    action: 'warn',
  },
  harassment: {
    enabled: true,
    patterns: [
      // Add harassment patterns here
    ],
    action: 'ban',
  },
  nsfw: {
    enabled: true,
    action: 'remove',
  },
};

// Rule violation types
export const VIOLATION_TYPES = {
  PROFANITY: 'profanity',
  SPAM: 'spam',
  HARASSMENT: 'harassment',
  NSFW: 'nsfw',
  CUSTOM: 'custom',
};

// Rule violation actions
export const VIOLATION_ACTIONS = {
  WARN: 'warn',
  REMOVE: 'remove',
  BAN: 'ban',
};

export const checkContent = (content, type = 'text') => {
  const violations = [];

  // Check for profanity
  if (CONTENT_RULES.profanity.enabled) {
    const profanityViolations = checkProfanity(content);
    if (profanityViolations.length > 0) {
      violations.push({
        type: VIOLATION_TYPES.PROFANITY,
        action: CONTENT_RULES.profanity.action,
        details: profanityViolations,
      });
    }
  }

  // Check for spam
  if (CONTENT_RULES.spam.enabled) {
    const spamViolations = checkSpam(content);
    if (spamViolations.length > 0) {
      violations.push({
        type: VIOLATION_TYPES.SPAM,
        action: CONTENT_RULES.spam.action,
        details: spamViolations,
      });
    }
  }

  // Check for harassment
  if (CONTENT_RULES.harassment.enabled) {
    const harassmentViolations = checkHarassment(content);
    if (harassmentViolations.length > 0) {
      violations.push({
        type: VIOLATION_TYPES.HARASSMENT,
        action: CONTENT_RULES.harassment.action,
        details: harassmentViolations,
      });
    }
  }

  // Check for NSFW content in images
  if (type === 'image' && CONTENT_RULES.nsfw.enabled) {
    const nsfwViolations = checkNSFW(content);
    if (nsfwViolations.length > 0) {
      violations.push({
        type: VIOLATION_TYPES.NSFW,
        action: CONTENT_RULES.nsfw.action,
        details: nsfwViolations,
      });
    }
  }

  return violations;
};

const checkProfanity = (content) => {
  const violations = [];
  const words = content.toLowerCase().split(/\s+/);
  
  words.forEach((word) => {
    if (CONTENT_RULES.profanity.words.includes(word)) {
      violations.push({
        word,
        context: getWordContext(content, word),
      });
    }
  });

  return violations;
};

const checkSpam = (content) => {
  const violations = [];
  
  // Check for too many links
  const linkCount = (content.match(/https?:\/\/[^\s]+/g) || []).length;
  if (linkCount > CONTENT_RULES.spam.maxLinks) {
    violations.push({
      type: 'links',
      count: linkCount,
      max: CONTENT_RULES.spam.maxLinks,
    });
  }

  // Check for too many mentions
  const mentionCount = (content.match(/@\w+/g) || []).length;
  if (mentionCount > CONTENT_RULES.spam.maxMentions) {
    violations.push({
      type: 'mentions',
      count: mentionCount,
      max: CONTENT_RULES.spam.maxMentions,
    });
  }

  return violations;
};

const checkHarassment = (content) => {
  const violations = [];
  
  CONTENT_RULES.harassment.patterns.forEach((pattern) => {
    if (new RegExp(pattern, 'i').test(content)) {
      violations.push({
        pattern,
        context: getPatternContext(content, pattern),
      });
    }
  });

  return violations;
};

const checkNSFW = async (imageUrl) => {
  // Implement NSFW image detection using a service like Google Cloud Vision API
  // This is a placeholder implementation
  return [];
};

const getWordContext = (content, word) => {
  const index = content.toLowerCase().indexOf(word);
  const start = Math.max(0, index - 20);
  const end = Math.min(content.length, index + word.length + 20);
  return content.slice(start, end);
};

const getPatternContext = (content, pattern) => {
  const match = content.match(new RegExp(pattern, 'i'));
  if (!match) return '';
  
  const index = match.index;
  const start = Math.max(0, index - 20);
  const end = Math.min(content.length, index + match[0].length + 20);
  return content.slice(start, end);
};

export const getViolationMessage = (violation) => {
  switch (violation.type) {
    case VIOLATION_TYPES.PROFANITY:
      return 'Your content contains inappropriate language.';
    case VIOLATION_TYPES.SPAM:
      return 'Your content contains too many links or mentions.';
    case VIOLATION_TYPES.HARASSMENT:
      return 'Your content violates our harassment policy.';
    case VIOLATION_TYPES.NSFW:
      return 'Your content contains inappropriate images.';
    default:
      return 'Your content violates our community guidelines.';
  }
};

export const getViolationAction = (violation) => {
  return violation.action || VIOLATION_ACTIONS.WARN;
}; 